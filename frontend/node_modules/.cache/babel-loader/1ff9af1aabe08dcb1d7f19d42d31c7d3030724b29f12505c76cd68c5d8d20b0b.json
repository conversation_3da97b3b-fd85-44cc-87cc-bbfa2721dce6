{"ast": null, "code": "import React from'react';import{useLocation,useNavigate}from'react-router-dom';import{Drawer,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Toolbar,Box}from'@mui/material';import{Dashboard,Topic,GroupWork,Message,Send,Storage,Settings,People}from'@mui/icons-material';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Sidebar=_ref=>{let{drawerWidth,mobileOpen,onDrawerToggle,isMobile}=_ref;const location=useLocation();const navigate=useNavigate();const{user}=useAuth();// Define menu items based on user role\nconst getMenuItems=()=>{const baseItems=[{text:'Topics',icon:/*#__PURE__*/_jsx(Topic,{}),path:'/topics'},{text:'Message Browser',icon:/*#__PURE__*/_jsx(Message,{}),path:'/messages'},{text:'Producer',icon:/*#__PURE__*/_jsx(Send,{}),path:'/producer'}];// Admin-only items\nconst adminItems=[{text:'Dashboard',icon:/*#__PURE__*/_jsx(Dashboard,{}),path:'/'},{text:'Consumer Groups',icon:/*#__PURE__*/_jsx(GroupWork,{}),path:'/consumer-groups'},{text:'Cluster Info',icon:/*#__PURE__*/_jsx(Storage,{}),path:'/cluster'},{text:'User Management',icon:/*#__PURE__*/_jsx(People,{}),path:'/user-management'},{text:'Settings',icon:/*#__PURE__*/_jsx(Settings,{}),path:'/settings'}];// Return appropriate items based on user role\nif((user===null||user===void 0?void 0:user.role)==='admin'){return[...adminItems,...baseItems];}else{return baseItems;}};const menuItems=getMenuItems();const handleNavigation=path=>{navigate(path);// Close mobile drawer after navigation\nif(isMobile){onDrawerToggle();}};const drawerContent=/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Toolbar,{}),/*#__PURE__*/_jsx(Box,{sx:{overflow:'auto'},children:/*#__PURE__*/_jsx(List,{children:menuItems.map(item=>/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsxs(ListItemButton,{selected:location.pathname===item.path,onClick:()=>handleNavigation(item.path),sx:{'&.Mui-selected':{backgroundColor:'rgba(25, 118, 210, 0.08)',borderRight:'3px solid #1976d2'},'&:hover':{backgroundColor:'rgba(25, 118, 210, 0.04)'},minHeight:{xs:48,sm:56},px:{xs:2,sm:3}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{color:location.pathname===item.path?'#1976d2':'inherit',minWidth:{xs:36,sm:40}},children:item.icon}),/*#__PURE__*/_jsx(ListItemText,{primary:item.text,sx:{color:location.pathname===item.path?'#1976d2':'inherit','& .MuiListItemText-primary':{fontSize:{xs:'0.875rem',sm:'1rem'},fontWeight:location.pathname===item.path?600:400}}})]})},item.text))})})]});return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Drawer,{variant:\"temporary\",open:mobileOpen,onClose:onDrawerToggle,ModalProps:{keepMounted:true// Better open performance on mobile.\n},sx:{display:{xs:'block',md:'none'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:drawerWidth,backgroundColor:'background.paper',borderRight:'1px solid rgba(0, 0, 0, 0.12)'}},children:drawerContent}),/*#__PURE__*/_jsx(Drawer,{variant:\"permanent\",sx:{display:{xs:'none',md:'block'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:drawerWidth,backgroundColor:'background.paper',borderRight:'1px solid rgba(0, 0, 0, 0.12)'}},open:true,children:drawerContent})]});};export default Sidebar;", "map": {"version": 3, "names": ["React", "useLocation", "useNavigate", "Drawer", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON><PERSON>", "Box", "Dashboard", "Topic", "GroupWork", "Message", "Send", "Storage", "Settings", "People", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Sidebar", "_ref", "drawerWidth", "mobileOpen", "onDrawerToggle", "isMobile", "location", "navigate", "user", "getMenuItems", "baseItems", "text", "icon", "path", "adminItems", "role", "menuItems", "handleNavigation", "drawerContent", "children", "sx", "overflow", "map", "item", "disablePadding", "selected", "pathname", "onClick", "backgroundColor", "borderRight", "minHeight", "xs", "sm", "px", "color", "min<PERSON><PERSON><PERSON>", "primary", "fontSize", "fontWeight", "variant", "open", "onClose", "ModalProps", "keepMounted", "display", "md", "boxSizing", "width"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport {\n  Drawer,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Toolbar,\n  Box,\n} from '@mui/material';\nimport {\n  Dashboard,\n  Topic,\n  GroupWork,\n  Message,\n  Send,\n  Storage,\n  Settings,\n  People,\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Sidebar = ({ drawerWidth, mobileOpen, onDrawerToggle, isMobile }) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n\n  // Define menu items based on user role\n  const getMenuItems = () => {\n    const baseItems = [\n      { text: 'Topics', icon: <Topic />, path: '/topics' },\n      { text: 'Message Browser', icon: <Message />, path: '/messages' },\n      { text: 'Producer', icon: <Send />, path: '/producer' },\n    ];\n\n    // Admin-only items\n    const adminItems = [\n      { text: 'Dashboard', icon: <Dashboard />, path: '/' },\n      { text: 'Consumer Groups', icon: <GroupWork />, path: '/consumer-groups' },\n      { text: 'Cluster Info', icon: <Storage />, path: '/cluster' },\n      { text: 'User Management', icon: <People />, path: '/user-management' },\n      { text: 'Settings', icon: <Settings />, path: '/settings' },\n    ];\n\n    // Return appropriate items based on user role\n    if (user?.role === 'admin') {\n      return [...adminItems, ...baseItems];\n    } else {\n      return baseItems;\n    }\n  };\n\n  const menuItems = getMenuItems();\n\n  const handleNavigation = (path) => {\n    navigate(path);\n    // Close mobile drawer after navigation\n    if (isMobile) {\n      onDrawerToggle();\n    }\n  };\n\n  const drawerContent = (\n    <>\n      <Toolbar />\n      <Box sx={{ overflow: 'auto' }}>\n        <List>\n          {menuItems.map((item) => (\n            <ListItem key={item.text} disablePadding>\n              <ListItemButton\n                selected={location.pathname === item.path}\n                onClick={() => handleNavigation(item.path)}\n                sx={{\n                  '&.Mui-selected': {\n                    backgroundColor: 'rgba(25, 118, 210, 0.08)',\n                    borderRight: '3px solid #1976d2',\n                  },\n                  '&:hover': {\n                    backgroundColor: 'rgba(25, 118, 210, 0.04)',\n                  },\n                  minHeight: { xs: 48, sm: 56 },\n                  px: { xs: 2, sm: 3 },\n                }}\n              >\n                <ListItemIcon\n                  sx={{\n                    color: location.pathname === item.path ? '#1976d2' : 'inherit',\n                    minWidth: { xs: 36, sm: 40 },\n                  }}\n                >\n                  {item.icon}\n                </ListItemIcon>\n                <ListItemText\n                  primary={item.text}\n                  sx={{\n                    color: location.pathname === item.path ? '#1976d2' : 'inherit',\n                    '& .MuiListItemText-primary': {\n                      fontSize: { xs: '0.875rem', sm: '1rem' },\n                      fontWeight: location.pathname === item.path ? 600 : 400,\n                    },\n                  }}\n                />\n              </ListItemButton>\n            </ListItem>\n          ))}\n        </List>\n      </Box>\n    </>\n  );\n\n  return (\n    <>\n      {/* Mobile drawer */}\n      <Drawer\n        variant=\"temporary\"\n        open={mobileOpen}\n        onClose={onDrawerToggle}\n        ModalProps={{\n          keepMounted: true, // Better open performance on mobile.\n        }}\n        sx={{\n          display: { xs: 'block', md: 'none' },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth,\n            backgroundColor: 'background.paper',\n            borderRight: '1px solid rgba(0, 0, 0, 0.12)',\n          },\n        }}\n      >\n        {drawerContent}\n      </Drawer>\n      \n      {/* Desktop drawer */}\n      <Drawer\n        variant=\"permanent\"\n        sx={{\n          display: { xs: 'none', md: 'block' },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth,\n            backgroundColor: 'background.paper',\n            borderRight: '1px solid rgba(0, 0, 0, 0.12)',\n          },\n        }}\n        open\n      >\n        {drawerContent}\n      </Drawer>\n    </>\n  );\n};\n\nexport default Sidebar;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,cAAc,CACdC,YAAY,CACZC,YAAY,CACZC,OAAO,CACPC,GAAG,KACE,eAAe,CACtB,OACEC,SAAS,CACTC,KAAK,CACLC,SAAS,CACTC,OAAO,CACPC,IAAI,CACJC,OAAO,CACPC,QAAQ,CACRC,MAAM,KACD,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAAC,OAAO,CAAGC,IAAA,EAA2D,IAA1D,CAAEC,WAAW,CAAEC,UAAU,CAAEC,cAAc,CAAEC,QAAS,CAAC,CAAAJ,IAAA,CACpE,KAAM,CAAAK,QAAQ,CAAG/B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgC,QAAQ,CAAG/B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEgC,IAAK,CAAC,CAAGf,OAAO,CAAC,CAAC,CAE1B;AACA,KAAM,CAAAgB,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,cAAEjB,IAAA,CAACT,KAAK,GAAE,CAAC,CAAE2B,IAAI,CAAE,SAAU,CAAC,CACpD,CAAEF,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEjB,IAAA,CAACP,OAAO,GAAE,CAAC,CAAEyB,IAAI,CAAE,WAAY,CAAC,CACjE,CAAEF,IAAI,CAAE,UAAU,CAAEC,IAAI,cAAEjB,IAAA,CAACN,IAAI,GAAE,CAAC,CAAEwB,IAAI,CAAE,WAAY,CAAC,CACxD,CAED;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEH,IAAI,CAAE,WAAW,CAAEC,IAAI,cAAEjB,IAAA,CAACV,SAAS,GAAE,CAAC,CAAE4B,IAAI,CAAE,GAAI,CAAC,CACrD,CAAEF,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEjB,IAAA,CAACR,SAAS,GAAE,CAAC,CAAE0B,IAAI,CAAE,kBAAmB,CAAC,CAC1E,CAAEF,IAAI,CAAE,cAAc,CAAEC,IAAI,cAAEjB,IAAA,CAACL,OAAO,GAAE,CAAC,CAAEuB,IAAI,CAAE,UAAW,CAAC,CAC7D,CAAEF,IAAI,CAAE,iBAAiB,CAAEC,IAAI,cAAEjB,IAAA,CAACH,MAAM,GAAE,CAAC,CAAEqB,IAAI,CAAE,kBAAmB,CAAC,CACvE,CAAEF,IAAI,CAAE,UAAU,CAAEC,IAAI,cAAEjB,IAAA,CAACJ,QAAQ,GAAE,CAAC,CAAEsB,IAAI,CAAE,WAAY,CAAC,CAC5D,CAED;AACA,GAAI,CAAAL,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEO,IAAI,IAAK,OAAO,CAAE,CAC1B,MAAO,CAAC,GAAGD,UAAU,CAAE,GAAGJ,SAAS,CAAC,CACtC,CAAC,IAAM,CACL,MAAO,CAAAA,SAAS,CAClB,CACF,CAAC,CAED,KAAM,CAAAM,SAAS,CAAGP,YAAY,CAAC,CAAC,CAEhC,KAAM,CAAAQ,gBAAgB,CAAIJ,IAAI,EAAK,CACjCN,QAAQ,CAACM,IAAI,CAAC,CACd;AACA,GAAIR,QAAQ,CAAE,CACZD,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAED,KAAM,CAAAc,aAAa,cACjBrB,KAAA,CAAAE,SAAA,EAAAoB,QAAA,eACExB,IAAA,CAACZ,OAAO,GAAE,CAAC,cACXY,IAAA,CAACX,GAAG,EAACoC,EAAE,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,cAC5BxB,IAAA,CAACjB,IAAI,EAAAyC,QAAA,CACFH,SAAS,CAACM,GAAG,CAAEC,IAAI,eAClB5B,IAAA,CAAChB,QAAQ,EAAiB6C,cAAc,MAAAL,QAAA,cACtCtB,KAAA,CAACjB,cAAc,EACb6C,QAAQ,CAAEnB,QAAQ,CAACoB,QAAQ,GAAKH,IAAI,CAACV,IAAK,CAC1Cc,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACM,IAAI,CAACV,IAAI,CAAE,CAC3CO,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBQ,eAAe,CAAE,0BAA0B,CAC3CC,WAAW,CAAE,mBACf,CAAC,CACD,SAAS,CAAE,CACTD,eAAe,CAAE,0BACnB,CAAC,CACDE,SAAS,CAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAC7BC,EAAE,CAAE,CAAEF,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CACrB,CAAE,CAAAb,QAAA,eAEFxB,IAAA,CAACd,YAAY,EACXuC,EAAE,CAAE,CACFc,KAAK,CAAE5B,QAAQ,CAACoB,QAAQ,GAAKH,IAAI,CAACV,IAAI,CAAG,SAAS,CAAG,SAAS,CAC9DsB,QAAQ,CAAE,CAAEJ,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAC7B,CAAE,CAAAb,QAAA,CAEDI,IAAI,CAACX,IAAI,CACE,CAAC,cACfjB,IAAA,CAACb,YAAY,EACXsD,OAAO,CAAEb,IAAI,CAACZ,IAAK,CACnBS,EAAE,CAAE,CACFc,KAAK,CAAE5B,QAAQ,CAACoB,QAAQ,GAAKH,IAAI,CAACV,IAAI,CAAG,SAAS,CAAG,SAAS,CAC9D,4BAA4B,CAAE,CAC5BwB,QAAQ,CAAE,CAAEN,EAAE,CAAE,UAAU,CAAEC,EAAE,CAAE,MAAO,CAAC,CACxCM,UAAU,CAAEhC,QAAQ,CAACoB,QAAQ,GAAKH,IAAI,CAACV,IAAI,CAAG,GAAG,CAAG,GACtD,CACF,CAAE,CACH,CAAC,EACY,CAAC,EAlCJU,IAAI,CAACZ,IAmCV,CACX,CAAC,CACE,CAAC,CACJ,CAAC,EACN,CACH,CAED,mBACEd,KAAA,CAAAE,SAAA,EAAAoB,QAAA,eAEExB,IAAA,CAAClB,MAAM,EACL8D,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAErC,UAAW,CACjBsC,OAAO,CAAErC,cAAe,CACxBsC,UAAU,CAAE,CACVC,WAAW,CAAE,IAAM;AACrB,CAAE,CACFvB,EAAE,CAAE,CACFwB,OAAO,CAAE,CAAEb,EAAE,CAAE,OAAO,CAAEc,EAAE,CAAE,MAAO,CAAC,CACpC,oBAAoB,CAAE,CACpBC,SAAS,CAAE,YAAY,CACvBC,KAAK,CAAE7C,WAAW,CAClB0B,eAAe,CAAE,kBAAkB,CACnCC,WAAW,CAAE,+BACf,CACF,CAAE,CAAAV,QAAA,CAEDD,aAAa,CACR,CAAC,cAGTvB,IAAA,CAAClB,MAAM,EACL8D,OAAO,CAAC,WAAW,CACnBnB,EAAE,CAAE,CACFwB,OAAO,CAAE,CAAEb,EAAE,CAAE,MAAM,CAAEc,EAAE,CAAE,OAAQ,CAAC,CACpC,oBAAoB,CAAE,CACpBC,SAAS,CAAE,YAAY,CACvBC,KAAK,CAAE7C,WAAW,CAClB0B,eAAe,CAAE,kBAAkB,CACnCC,WAAW,CAAE,+BACf,CACF,CAAE,CACFW,IAAI,MAAArB,QAAA,CAEHD,aAAa,CACR,CAAC,EACT,CAAC,CAEP,CAAC,CAED,cAAe,CAAAlB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}