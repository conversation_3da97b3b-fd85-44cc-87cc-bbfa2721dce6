{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Projects/Kafka-dashboard/frontend/src/contexts/AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [userTopics, setUserTopics] = useState(null);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      fetchUserProfile();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const fetchUserProfile = async () => {\n    try {\n      const response = await authApi.getProfile();\n      if (response.success) {\n        setUser(response.data);\n        // Fetch user's accessible topics\n        fetchUserTopics();\n      } else {\n        localStorage.removeItem('token');\n        setUser(null);\n      }\n    } catch (error) {\n      console.error('Error fetching user profile:', error);\n      localStorage.removeItem('token');\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchUserTopics = async () => {\n    try {\n      const response = await authApi.getUserTopics();\n      if (response.success) {\n        setUserTopics(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching user topics:', error);\n    }\n  };\n  const login = async credentials => {\n    try {\n      const response = await authApi.login(credentials);\n      if (response.success) {\n        const {\n          token,\n          user\n        } = response.data;\n        localStorage.setItem('token', token);\n        setUser(user);\n        await fetchUserTopics();\n        return {\n          success: true\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: error.message || 'Login failed'\n      };\n    }\n  };\n  const logout = async () => {\n    try {\n      await authApi.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setUser(null);\n      setUserTopics(null);\n    }\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await authApi.updateProfile(profileData);\n      if (response.success) {\n        setUser(response.data);\n        return {\n          success: true\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: error.message || 'Profile update failed'\n      };\n    }\n  };\n  const changePassword = async passwordData => {\n    try {\n      const response = await authApi.changePassword(passwordData);\n      if (response.success) {\n        return {\n          success: true\n        };\n      } else {\n        return {\n          success: false,\n          message: response.message\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: error.message || 'Password change failed'\n      };\n    }\n  };\n\n  // Helper functions for role-based access\n  const isAdmin = () => (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n  const isTopicOwner = () => (user === null || user === void 0 ? void 0 : user.role) === 'topic_owner';\n  const isViewer = () => (user === null || user === void 0 ? void 0 : user.role) === 'viewer';\n  const canAccessTopic = (topicName, permission = 'read') => {\n    if (isAdmin()) return true;\n    if (!(userTopics !== null && userTopics !== void 0 && userTopics.topicPermissions)) return false;\n\n    // For non-admin users, check if they have the specific permission for the topic\n    const topicPerms = userTopics.topicPermissions[topicName];\n    return topicPerms && topicPerms.includes(permission);\n  };\n  const canCreateTopics = () => {\n    return isAdmin();\n  };\n  const canProduceToTopic = topicName => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'write');\n  };\n  const canBrowseTopic = topicName => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'read');\n  };\n  const canDeleteTopic = topicName => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'delete');\n  };\n  const canConfigureTopic = topicName => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'configure');\n  };\n  const canAccessAdminPages = () => {\n    return isAdmin();\n  };\n  const getAccessibleTopics = () => {\n    return (userTopics === null || userTopics === void 0 ? void 0 : userTopics.accessibleTopics) || [];\n  };\n  const value = {\n    user,\n    userTopics,\n    loading,\n    login,\n    logout,\n    updateProfile,\n    changePassword,\n    fetchUserProfile,\n    // Role-based helpers\n    isAdmin,\n    isTopicOwner,\n    isViewer,\n    canAccessTopic,\n    canCreateTopics,\n    canProduceToTopic,\n    canBrowseTopic,\n    canDeleteTopic,\n    canConfigureTopic,\n    canAccessAdminPages,\n    getAccessibleTopics\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"SoUTmKaeFBXvaCXyxXqsXx2okio=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authApi", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "userTopics", "setUserTopics", "token", "localStorage", "getItem", "fetchUserProfile", "response", "getProfile", "success", "data", "fetchUserTopics", "removeItem", "error", "console", "getUserTopics", "login", "credentials", "setItem", "message", "logout", "updateProfile", "profileData", "changePassword", "passwordData", "isAdmin", "role", "isTopic<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "canAccessTopic", "topicName", "permission", "topicPermissions", "topicPerms", "includes", "canCreateTopics", "canProduceToTopic", "canBrowseTopic", "canDeleteTopic", "canConfigureTopic", "canAccessAdminPages", "getAccessibleTopics", "accessibleTopics", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authApi } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [userTopics, setUserTopics] = useState(null);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      fetchUserProfile();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  const fetchUserProfile = async () => {\n    try {\n      const response = await authApi.getProfile();\n      if (response.success) {\n        setUser(response.data);\n        // Fetch user's accessible topics\n        fetchUserTopics();\n      } else {\n        localStorage.removeItem('token');\n        setUser(null);\n      }\n    } catch (error) {\n      console.error('Error fetching user profile:', error);\n      localStorage.removeItem('token');\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUserTopics = async () => {\n    try {\n      const response = await authApi.getUserTopics();\n      if (response.success) {\n        setUserTopics(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching user topics:', error);\n    }\n  };\n\n  const login = async (credentials) => {\n    try {\n      const response = await authApi.login(credentials);\n      if (response.success) {\n        const { token, user } = response.data;\n        localStorage.setItem('token', token);\n        setUser(user);\n        await fetchUserTopics();\n        return { success: true };\n      } else {\n        return { success: false, message: response.message };\n      }\n    } catch (error) {\n      return { \n        success: false, \n        message: error.message || 'Login failed' \n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authApi.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setUser(null);\n      setUserTopics(null);\n    }\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await authApi.updateProfile(profileData);\n      if (response.success) {\n        setUser(response.data);\n        return { success: true };\n      } else {\n        return { success: false, message: response.message };\n      }\n    } catch (error) {\n      return { \n        success: false, \n        message: error.message || 'Profile update failed' \n      };\n    }\n  };\n\n  const changePassword = async (passwordData) => {\n    try {\n      const response = await authApi.changePassword(passwordData);\n      if (response.success) {\n        return { success: true };\n      } else {\n        return { success: false, message: response.message };\n      }\n    } catch (error) {\n      return { \n        success: false, \n        message: error.message || 'Password change failed' \n      };\n    }\n  };\n\n  // Helper functions for role-based access\n  const isAdmin = () => user?.role === 'admin';\n  const isTopicOwner = () => user?.role === 'topic_owner';\n  const isViewer = () => user?.role === 'viewer';\n\n  const canAccessTopic = (topicName, permission = 'read') => {\n    if (isAdmin()) return true;\n\n    if (!userTopics?.topicPermissions) return false;\n\n    // For non-admin users, check if they have the specific permission for the topic\n    const topicPerms = userTopics.topicPermissions[topicName];\n    return topicPerms && topicPerms.includes(permission);\n  };\n\n  const canCreateTopics = () => {\n    return isAdmin();\n  };\n\n  const canProduceToTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'write');\n  };\n\n  const canBrowseTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'read');\n  };\n\n  const canDeleteTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'delete');\n  };\n\n  const canConfigureTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'configure');\n  };\n\n  const canAccessAdminPages = () => {\n    return isAdmin();\n  };\n\n  const getAccessibleTopics = () => {\n    return userTopics?.accessibleTopics || [];\n  };\n\n  const value = {\n    user,\n    userTopics,\n    loading,\n    login,\n    logout,\n    updateProfile,\n    changePassword,\n    fetchUserProfile,\n    // Role-based helpers\n    isAdmin,\n    isTopicOwner,\n    isViewer,\n    canAccessTopic,\n    canCreateTopics,\n    canProduceToTopic,\n    canBrowseTopic,\n    canDeleteTopic,\n    canConfigureTopic,\n    canAccessAdminPages,\n    getAccessibleTopics,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTG,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,OAAO,CAACsB,UAAU,CAAC,CAAC;MAC3C,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBX,OAAO,CAACS,QAAQ,CAACG,IAAI,CAAC;QACtB;QACAC,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACLP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;QAChCd,OAAO,CAAC,IAAI,CAAC;MACf;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDT,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;MAChCd,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAMrB,OAAO,CAAC6B,aAAa,CAAC,CAAC;MAC9C,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpBP,aAAa,CAACK,QAAQ,CAACG,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMG,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMrB,OAAO,CAAC8B,KAAK,CAACC,WAAW,CAAC;MACjD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAM;UAAEN,KAAK;UAAEN;QAAK,CAAC,GAAGU,QAAQ,CAACG,IAAI;QACrCN,YAAY,CAACc,OAAO,CAAC,OAAO,EAAEf,KAAK,CAAC;QACpCL,OAAO,CAACD,IAAI,CAAC;QACb,MAAMc,eAAe,CAAC,CAAC;QACvB,OAAO;UAAEF,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL,OAAO;UAAEA,OAAO,EAAE,KAAK;UAAEU,OAAO,EAAEZ,QAAQ,CAACY;QAAQ,CAAC;MACtD;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdU,OAAO,EAAEN,KAAK,CAACM,OAAO,IAAI;MAC5B,CAAC;IACH;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMlC,OAAO,CAACkC,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRT,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;MAChCd,OAAO,CAAC,IAAI,CAAC;MACbI,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMmB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMrB,OAAO,CAACmC,aAAa,CAACC,WAAW,CAAC;MACzD,IAAIf,QAAQ,CAACE,OAAO,EAAE;QACpBX,OAAO,CAACS,QAAQ,CAACG,IAAI,CAAC;QACtB,OAAO;UAAED,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL,OAAO;UAAEA,OAAO,EAAE,KAAK;UAAEU,OAAO,EAAEZ,QAAQ,CAACY;QAAQ,CAAC;MACtD;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdU,OAAO,EAAEN,KAAK,CAACM,OAAO,IAAI;MAC5B,CAAC;IACH;EACF,CAAC;EAED,MAAMI,cAAc,GAAG,MAAOC,YAAY,IAAK;IAC7C,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMrB,OAAO,CAACqC,cAAc,CAACC,YAAY,CAAC;MAC3D,IAAIjB,QAAQ,CAACE,OAAO,EAAE;QACpB,OAAO;UAAEA,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL,OAAO;UAAEA,OAAO,EAAE,KAAK;UAAEU,OAAO,EAAEZ,QAAQ,CAACY;QAAQ,CAAC;MACtD;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,OAAO;QACLJ,OAAO,EAAE,KAAK;QACdU,OAAO,EAAEN,KAAK,CAACM,OAAO,IAAI;MAC5B,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMM,OAAO,GAAGA,CAAA,KAAM,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,OAAO;EAC5C,MAAMC,YAAY,GAAGA,CAAA,KAAM,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,aAAa;EACvD,MAAME,QAAQ,GAAGA,CAAA,KAAM,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,QAAQ;EAE9C,MAAMG,cAAc,GAAGA,CAACC,SAAS,EAAEC,UAAU,GAAG,MAAM,KAAK;IACzD,IAAIN,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;IAE1B,IAAI,EAACxB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE+B,gBAAgB,GAAE,OAAO,KAAK;;IAE/C;IACA,MAAMC,UAAU,GAAGhC,UAAU,CAAC+B,gBAAgB,CAACF,SAAS,CAAC;IACzD,OAAOG,UAAU,IAAIA,UAAU,CAACC,QAAQ,CAACH,UAAU,CAAC;EACtD,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOV,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,MAAMW,iBAAiB,GAAIN,SAAS,IAAK;IACvC,IAAIL,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;IAC1B,OAAOI,cAAc,CAACC,SAAS,EAAE,OAAO,CAAC;EAC3C,CAAC;EAED,MAAMO,cAAc,GAAIP,SAAS,IAAK;IACpC,IAAIL,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;IAC1B,OAAOI,cAAc,CAACC,SAAS,EAAE,MAAM,CAAC;EAC1C,CAAC;EAED,MAAMQ,cAAc,GAAIR,SAAS,IAAK;IACpC,IAAIL,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;IAC1B,OAAOI,cAAc,CAACC,SAAS,EAAE,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMS,iBAAiB,GAAIT,SAAS,IAAK;IACvC,IAAIL,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;IAC1B,OAAOI,cAAc,CAACC,SAAS,EAAE,WAAW,CAAC;EAC/C,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOf,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,MAAMgB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAO,CAAAxC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEyC,gBAAgB,KAAI,EAAE;EAC3C,CAAC;EAED,MAAMC,KAAK,GAAG;IACZ9C,IAAI;IACJI,UAAU;IACVF,OAAO;IACPiB,KAAK;IACLI,MAAM;IACNC,aAAa;IACbE,cAAc;IACdjB,gBAAgB;IAChB;IACAmB,OAAO;IACPE,YAAY;IACZC,QAAQ;IACRC,cAAc;IACdM,eAAe;IACfC,iBAAiB;IACjBC,cAAc;IACdC,cAAc;IACdC,iBAAiB;IACjBC,mBAAmB;IACnBC;EACF,CAAC;EAED,oBACErD,OAAA,CAACC,WAAW,CAACuD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAhD,QAAA,EAChCA;EAAQ;IAAAkD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACpD,GAAA,CA1LWF,YAAY;AAAAuD,EAAA,GAAZvD,YAAY;AAAA,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}