{"ast": null, "code": "import React from'react';import{Box,Typography,Grid,Card,CardContent,Paper,LinearProgress,Chip,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Alert,CircularProgress}from'@mui/material';import{Memory,Speed,Storage,NetworkCheck,CheckCircle,Error,Warning}from'@mui/icons-material';import{useQuery}from'react-query';import{clusterApi}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MetricCard=_ref=>{let{title,value,icon,color='primary',subtitle,trend}=_ref;return/*#__PURE__*/_jsx(Card,{sx:{height:'100%'},children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,variant:\"h6\",children:title}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h2\",sx:{mb:1},children:value}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:subtitle}),trend&&/*#__PURE__*/_jsx(Chip,{label:trend,size:\"small\",color:trend.includes('+')?'success':'error',sx:{mt:1}})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',width:60,height:60,borderRadius:'50%',backgroundColor:\"\".concat(color,\".light\"),color:\"\".concat(color,\".main\")},children:icon})]})})});};const ProgressBar=_ref2=>{let{value,label,color='primary'}=_ref2;return/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:label}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"bold\",children:[value.toFixed(1),\"%\"]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:value,sx:{height:8,borderRadius:4,backgroundColor:'grey.200','& .MuiLinearProgress-bar':{borderRadius:4,backgroundColor:color==='error'?'error.main':color==='warning'?'warning.main':color==='success'?'success.main':'primary.main'}}})]});};const Analytics=()=>{var _brokerMetrics$data,_brokerMetrics$data2,_brokerMetrics$data3,_clusterMetrics$avera,_clusterMetrics$avera2,_clusterMetrics$avera3;const{data:brokerMetrics,isLoading,error}=useQuery('broker-metrics',clusterApi.getBrokerMetrics,{refetchInterval:30000// Refresh every 30 seconds\n});if(isLoading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}if(error){return/*#__PURE__*/_jsx(Box,{sx:{mt:2},children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:\"Failed to load broker metrics. Please check your connection.\"})});}const brokers=(brokerMetrics===null||brokerMetrics===void 0?void 0:(_brokerMetrics$data=brokerMetrics.data)===null||_brokerMetrics$data===void 0?void 0:_brokerMetrics$data.brokers)||[];const clusterMetrics=(brokerMetrics===null||brokerMetrics===void 0?void 0:(_brokerMetrics$data2=brokerMetrics.data)===null||_brokerMetrics$data2===void 0?void 0:_brokerMetrics$data2.cluster)||{};const timestamp=brokerMetrics===null||brokerMetrics===void 0?void 0:(_brokerMetrics$data3=brokerMetrics.data)===null||_brokerMetrics$data3===void 0?void 0:_brokerMetrics$data3.timestamp;// Helper function to get color based on utilization\nconst getUtilizationColor=value=>{if(value>=80)return'error';if(value>=60)return'warning';return'success';};// Helper function to format bytes\nconst formatBytes=bytes=>{if(bytes===0)return'0 B';const k=1024;const sizes=['B','KB','MB','GB','TB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(2))+' '+sizes[i];};return/*#__PURE__*/_jsxs(Box,{sx:{flexGrow:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{mb:4},children:\"Broker Analytics\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:4},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(MetricCard,{title:\"Average CPU\",value:\"\".concat(((_clusterMetrics$avera=clusterMetrics.averageCpu)===null||_clusterMetrics$avera===void 0?void 0:_clusterMetrics$avera.toFixed(1))||0,\"%\"),icon:/*#__PURE__*/_jsx(Speed,{}),color:getUtilizationColor(clusterMetrics.averageCpu||0),subtitle:\"\".concat(clusterMetrics.onlineBrokers||0,\"/\").concat(clusterMetrics.totalBrokers||0,\" brokers online\")})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(MetricCard,{title:\"Average Memory\",value:\"\".concat(((_clusterMetrics$avera2=clusterMetrics.averageMemory)===null||_clusterMetrics$avera2===void 0?void 0:_clusterMetrics$avera2.toFixed(1))||0,\"%\"),icon:/*#__PURE__*/_jsx(Memory,{}),color:getUtilizationColor(clusterMetrics.averageMemory||0),subtitle:\"RAM utilization\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(MetricCard,{title:\"Average Disk\",value:\"\".concat(((_clusterMetrics$avera3=clusterMetrics.averageDisk)===null||_clusterMetrics$avera3===void 0?void 0:_clusterMetrics$avera3.toFixed(1))||0,\"%\"),icon:/*#__PURE__*/_jsx(Storage,{}),color:getUtilizationColor(clusterMetrics.averageDisk||0),subtitle:\"Storage utilization\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsx(MetricCard,{title:\"Broker Status\",value:\"\".concat(clusterMetrics.onlineBrokers||0,\"/\").concat(clusterMetrics.totalBrokers||0),icon:/*#__PURE__*/_jsx(CheckCircle,{}),color:clusterMetrics.onlineBrokers===clusterMetrics.totalBrokers?'success':'warning',subtitle:\"Online brokers\"})})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:2,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:2},children:\"Broker Details\"}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"Broker ID\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Host:Port\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Status\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Role\"}),/*#__PURE__*/_jsx(TableCell,{children:\"CPU\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Memory\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Disk\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Network\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:brokers.map(broker=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",children:broker.nodeId})}),/*#__PURE__*/_jsxs(TableCell,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[broker.host,\":\",broker.port]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"textSecondary\",children:[\"Rack: \",broker.rack]})]}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:broker.status,size:\"small\",color:broker.status==='online'?'success':'error',icon:broker.status==='online'?/*#__PURE__*/_jsx(CheckCircle,{}):/*#__PURE__*/_jsx(Error,{})})}),/*#__PURE__*/_jsx(TableCell,{children:broker.isController?/*#__PURE__*/_jsx(Chip,{label:\"Controller\",size:\"small\",color:\"primary\",variant:\"outlined\"}):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Follower\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Box,{sx:{minWidth:100},children:/*#__PURE__*/_jsx(ProgressBar,{value:broker.metrics.cpu.utilization,label:\"CPU\",color:getUtilizationColor(broker.metrics.cpu.utilization)})})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{minWidth:100},children:[/*#__PURE__*/_jsx(ProgressBar,{value:broker.metrics.memory.utilization,label:\"Memory\",color:getUtilizationColor(broker.metrics.memory.utilization)}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"textSecondary\",children:[formatBytes(broker.metrics.memory.used*1024*1024),\" / \",formatBytes(broker.metrics.memory.total*1024*1024)]})]})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{minWidth:100},children:[/*#__PURE__*/_jsx(ProgressBar,{value:broker.metrics.disk.utilization,label:\"Disk\",color:getUtilizationColor(broker.metrics.disk.utilization)}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"textSecondary\",children:[formatBytes(broker.metrics.disk.used*1024*1024),\" / \",formatBytes(broker.metrics.disk.total*1024*1024)]})]})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontSize:\"0.75rem\",children:[/*#__PURE__*/_jsx(NetworkCheck,{sx:{fontSize:12,mr:0.5}}),formatBytes(broker.metrics.network.bytesIn),\"/s in\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontSize:\"0.75rem\",children:[formatBytes(broker.metrics.network.bytesOut),\"/s out\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"textSecondary\",children:[broker.metrics.network.requestsPerSec,\" req/s\"]})]})})]},broker.nodeId))})]})})]}),/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',mt:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"textSecondary\",children:[\"Last updated: \",timestamp?new Date(timestamp).toLocaleString():'Unknown']})})]});};export default Analytics;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "LinearProgress", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "CircularProgress", "Memory", "Speed", "Storage", "NetworkCheck", "CheckCircle", "Error", "Warning", "useQuery", "clusterApi", "jsx", "_jsx", "jsxs", "_jsxs", "MetricCard", "_ref", "title", "value", "icon", "color", "subtitle", "trend", "sx", "height", "children", "display", "alignItems", "justifyContent", "flex", "gutterBottom", "variant", "component", "mb", "label", "size", "includes", "mt", "width", "borderRadius", "backgroundColor", "concat", "ProgressBar", "_ref2", "fontWeight", "toFixed", "Analytics", "_brokerMetrics$data", "_brokerMetrics$data2", "_brokerMetrics$data3", "_clusterMetrics$avera", "_clusterMetrics$avera2", "_clusterMetrics$avera3", "data", "brokerMetrics", "isLoading", "error", "getBrokerMetrics", "refetchInterval", "minHeight", "severity", "brokers", "clusterMetrics", "cluster", "timestamp", "getUtilizationColor", "formatBytes", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "flexGrow", "container", "spacing", "item", "xs", "sm", "md", "averageCpu", "onlineBrokers", "totalBrokers", "averageMemory", "averageDisk", "p", "map", "broker", "nodeId", "host", "port", "rack", "status", "isController", "min<PERSON><PERSON><PERSON>", "metrics", "cpu", "utilization", "memory", "used", "total", "disk", "fontSize", "mr", "network", "bytesIn", "bytesOut", "requestsPerSec", "textAlign", "Date", "toLocaleString"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Paper,\n  LinearProgress,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Alert,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  Memory,\n  Speed,\n  Storage,\n  NetworkCheck,\n  CheckCircle,\n  Error,\n  Warning,\n} from '@mui/icons-material';\nimport { useQuery } from 'react-query';\nimport { clusterApi } from '../services/api';\n\nconst MetricCard = ({ title, value, icon, color = 'primary', subtitle, trend }) => (\n  <Card sx={{ height: '100%' }}>\n    <CardContent>\n      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n        <Box sx={{ flex: 1 }}>\n          <Typography color=\"textSecondary\" gutterBottom variant=\"h6\">\n            {title}\n          </Typography>\n          <Typography variant=\"h4\" component=\"h2\" sx={{ mb: 1 }}>\n            {value}\n          </Typography>\n          {subtitle && (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              {subtitle}\n            </Typography>\n          )}\n          {trend && (\n            <Chip\n              label={trend}\n              size=\"small\"\n              color={trend.includes('+') ? 'success' : 'error'}\n              sx={{ mt: 1 }}\n            />\n          )}\n        </Box>\n        <Box\n          sx={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: 60,\n            height: 60,\n            borderRadius: '50%',\n            backgroundColor: `${color}.light`,\n            color: `${color}.main`,\n          }}\n        >\n          {icon}\n        </Box>\n      </Box>\n    </CardContent>\n  </Card>\n);\n\nconst ProgressBar = ({ value, label, color = 'primary' }) => (\n  <Box sx={{ mb: 2 }}>\n    <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={1}>\n      <Typography variant=\"body2\" color=\"textSecondary\">\n        {label}\n      </Typography>\n      <Typography variant=\"body2\" fontWeight=\"bold\">\n        {value.toFixed(1)}%\n      </Typography>\n    </Box>\n    <LinearProgress\n      variant=\"determinate\"\n      value={value}\n      sx={{\n        height: 8,\n        borderRadius: 4,\n        backgroundColor: 'grey.200',\n        '& .MuiLinearProgress-bar': {\n          borderRadius: 4,\n          backgroundColor: color === 'error' ? 'error.main' : \n                          color === 'warning' ? 'warning.main' : \n                          color === 'success' ? 'success.main' : 'primary.main'\n        }\n      }}\n    />\n  </Box>\n);\n\nconst Analytics = () => {\n  const { data: brokerMetrics, isLoading, error } = useQuery('broker-metrics', clusterApi.getBrokerMetrics, {\n    refetchInterval: 30000, // Refresh every 30 seconds\n  });\n\n  if (isLoading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box sx={{ mt: 2 }}>\n        <Alert severity=\"error\">\n          Failed to load broker metrics. Please check your connection.\n        </Alert>\n      </Box>\n    );\n  }\n\n  const brokers = brokerMetrics?.data?.brokers || [];\n  const clusterMetrics = brokerMetrics?.data?.cluster || {};\n  const timestamp = brokerMetrics?.data?.timestamp;\n\n  // Helper function to get color based on utilization\n  const getUtilizationColor = (value) => {\n    if (value >= 80) return 'error';\n    if (value >= 60) return 'warning';\n    return 'success';\n  };\n\n  // Helper function to format bytes\n  const formatBytes = (bytes) => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <Box sx={{ flexGrow: 1 }}>\n      <Typography variant=\"h4\" sx={{ mb: 4 }}>\n        Broker Analytics\n      </Typography>\n\n      {/* Cluster Overview Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Average CPU\"\n            value={`${clusterMetrics.averageCpu?.toFixed(1) || 0}%`}\n            icon={<Speed />}\n            color={getUtilizationColor(clusterMetrics.averageCpu || 0)}\n            subtitle={`${clusterMetrics.onlineBrokers || 0}/${clusterMetrics.totalBrokers || 0} brokers online`}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Average Memory\"\n            value={`${clusterMetrics.averageMemory?.toFixed(1) || 0}%`}\n            icon={<Memory />}\n            color={getUtilizationColor(clusterMetrics.averageMemory || 0)}\n            subtitle=\"RAM utilization\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Average Disk\"\n            value={`${clusterMetrics.averageDisk?.toFixed(1) || 0}%`}\n            icon={<Storage />}\n            color={getUtilizationColor(clusterMetrics.averageDisk || 0)}\n            subtitle=\"Storage utilization\"\n          />\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Broker Status\"\n            value={`${clusterMetrics.onlineBrokers || 0}/${clusterMetrics.totalBrokers || 0}`}\n            icon={<CheckCircle />}\n            color={clusterMetrics.onlineBrokers === clusterMetrics.totalBrokers ? 'success' : 'warning'}\n            subtitle=\"Online brokers\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Broker Details Table */}\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>\n          Broker Details\n        </Typography>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Broker ID</TableCell>\n                <TableCell>Host:Port</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell>Role</TableCell>\n                <TableCell>CPU</TableCell>\n                <TableCell>Memory</TableCell>\n                <TableCell>Disk</TableCell>\n                <TableCell>Network</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {brokers.map((broker) => (\n                <TableRow key={broker.nodeId}>\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                      {broker.nodeId}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {broker.host}:{broker.port}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"textSecondary\">\n                      Rack: {broker.rack}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={broker.status}\n                      size=\"small\"\n                      color={broker.status === 'online' ? 'success' : 'error'}\n                      icon={broker.status === 'online' ? <CheckCircle /> : <Error />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    {broker.isController ? (\n                      <Chip\n                        label=\"Controller\"\n                        size=\"small\"\n                        color=\"primary\"\n                        variant=\"outlined\"\n                      />\n                    ) : (\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        Follower\n                      </Typography>\n                    )}\n                  </TableCell>\n                  <TableCell>\n                    <Box sx={{ minWidth: 100 }}>\n                      <ProgressBar\n                        value={broker.metrics.cpu.utilization}\n                        label=\"CPU\"\n                        color={getUtilizationColor(broker.metrics.cpu.utilization)}\n                      />\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Box sx={{ minWidth: 100 }}>\n                      <ProgressBar\n                        value={broker.metrics.memory.utilization}\n                        label=\"Memory\"\n                        color={getUtilizationColor(broker.metrics.memory.utilization)}\n                      />\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        {formatBytes(broker.metrics.memory.used * 1024 * 1024)} / {formatBytes(broker.metrics.memory.total * 1024 * 1024)}\n                      </Typography>\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Box sx={{ minWidth: 100 }}>\n                      <ProgressBar\n                        value={broker.metrics.disk.utilization}\n                        label=\"Disk\"\n                        color={getUtilizationColor(broker.metrics.disk.utilization)}\n                      />\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        {formatBytes(broker.metrics.disk.used * 1024 * 1024)} / {formatBytes(broker.metrics.disk.total * 1024 * 1024)}\n                      </Typography>\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Box>\n                      <Typography variant=\"body2\" fontSize=\"0.75rem\">\n                        <NetworkCheck sx={{ fontSize: 12, mr: 0.5 }} />\n                        {formatBytes(broker.metrics.network.bytesIn)}/s in\n                      </Typography>\n                      <Typography variant=\"body2\" fontSize=\"0.75rem\">\n                        {formatBytes(broker.metrics.network.bytesOut)}/s out\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        {broker.metrics.network.requestsPerSec} req/s\n                      </Typography>\n                    </Box>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </Paper>\n\n      {/* Last Updated */}\n      <Box sx={{ textAlign: 'center', mt: 2 }}>\n        <Typography variant=\"caption\" color=\"textSecondary\">\n          Last updated: {timestamp ? new Date(timestamp).toLocaleString() : 'Unknown'}\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Analytics; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,cAAc,CACdC,IAAI,CACJC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,KAAK,CACLC,gBAAgB,KACX,eAAe,CACtB,OACEC,MAAM,CACNC,KAAK,CACLC,OAAO,CACPC,YAAY,CACZC,WAAW,CACXC,KAAK,CACLC,OAAO,KACF,qBAAqB,CAC5B,OAASC,QAAQ,KAAQ,aAAa,CACtC,OAASC,UAAU,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7C,KAAM,CAAAC,UAAU,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,CAAG,SAAS,CAAEC,QAAQ,CAAEC,KAAM,CAAC,CAAAN,IAAA,oBAC5EJ,IAAA,CAACvB,IAAI,EAACkC,EAAE,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAAC,QAAA,cAC3Bb,IAAA,CAACtB,WAAW,EAAAmC,QAAA,cACVX,KAAA,CAAC5B,GAAG,EAACwC,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAAH,QAAA,eACpEX,KAAA,CAAC5B,GAAG,EAACqC,EAAE,CAAE,CAAEM,IAAI,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACnBb,IAAA,CAACzB,UAAU,EAACiC,KAAK,CAAC,eAAe,CAACU,YAAY,MAACC,OAAO,CAAC,IAAI,CAAAN,QAAA,CACxDR,KAAK,CACI,CAAC,cACbL,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACT,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CACnDP,KAAK,CACI,CAAC,CACZG,QAAQ,eACPT,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,CAC9CJ,QAAQ,CACC,CACb,CACAC,KAAK,eACJV,IAAA,CAACnB,IAAI,EACHyC,KAAK,CAAEZ,KAAM,CACba,IAAI,CAAC,OAAO,CACZf,KAAK,CAAEE,KAAK,CAACc,QAAQ,CAAC,GAAG,CAAC,CAAG,SAAS,CAAG,OAAQ,CACjDb,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CACf,CACF,EACE,CAAC,cACNzB,IAAA,CAAC1B,GAAG,EACFqC,EAAE,CAAE,CACFG,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBU,KAAK,CAAE,EAAE,CACTd,MAAM,CAAE,EAAE,CACVe,YAAY,CAAE,KAAK,CACnBC,eAAe,IAAAC,MAAA,CAAKrB,KAAK,UAAQ,CACjCA,KAAK,IAAAqB,MAAA,CAAKrB,KAAK,SACjB,CAAE,CAAAK,QAAA,CAEDN,IAAI,CACF,CAAC,EACH,CAAC,CACK,CAAC,CACV,CAAC,EACR,CAED,KAAM,CAAAuB,WAAW,CAAGC,KAAA,MAAC,CAAEzB,KAAK,CAAEgB,KAAK,CAAEd,KAAK,CAAG,SAAU,CAAC,CAAAuB,KAAA,oBACtD7B,KAAA,CAAC5B,GAAG,EAACqC,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACjBX,KAAA,CAAC5B,GAAG,EAACwC,OAAO,CAAC,MAAM,CAACE,cAAc,CAAC,eAAe,CAACD,UAAU,CAAC,QAAQ,CAACM,EAAE,CAAE,CAAE,CAAAR,QAAA,eAC3Eb,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,CAC9CS,KAAK,CACI,CAAC,cACbpB,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACa,UAAU,CAAC,MAAM,CAAAnB,QAAA,EAC1CP,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC,CAAC,GACpB,EAAY,CAAC,EACV,CAAC,cACNjC,IAAA,CAACpB,cAAc,EACbuC,OAAO,CAAC,aAAa,CACrBb,KAAK,CAAEA,KAAM,CACbK,EAAE,CAAE,CACFC,MAAM,CAAE,CAAC,CACTe,YAAY,CAAE,CAAC,CACfC,eAAe,CAAE,UAAU,CAC3B,0BAA0B,CAAE,CAC1BD,YAAY,CAAE,CAAC,CACfC,eAAe,CAAEpB,KAAK,GAAK,OAAO,CAAG,YAAY,CACjCA,KAAK,GAAK,SAAS,CAAG,cAAc,CACpCA,KAAK,GAAK,SAAS,CAAG,cAAc,CAAG,cACzD,CACF,CAAE,CACH,CAAC,EACC,CAAC,EACP,CAED,KAAM,CAAA0B,SAAS,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACtB,KAAM,CAAEC,IAAI,CAAEC,aAAa,CAAEC,SAAS,CAAEC,KAAM,CAAC,CAAG/C,QAAQ,CAAC,gBAAgB,CAAEC,UAAU,CAAC+C,gBAAgB,CAAE,CACxGC,eAAe,CAAE,KAAO;AAC1B,CAAC,CAAC,CAEF,GAAIH,SAAS,CAAE,CACb,mBACE3C,IAAA,CAAC1B,GAAG,EAACwC,OAAO,CAAC,MAAM,CAACE,cAAc,CAAC,QAAQ,CAACD,UAAU,CAAC,QAAQ,CAACgC,SAAS,CAAC,OAAO,CAAAlC,QAAA,cAC/Eb,IAAA,CAACX,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA,GAAIuD,KAAK,CAAE,CACT,mBACE5C,IAAA,CAAC1B,GAAG,EAACqC,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,cACjBb,IAAA,CAACZ,KAAK,EAAC4D,QAAQ,CAAC,OAAO,CAAAnC,QAAA,CAAC,8DAExB,CAAO,CAAC,CACL,CAAC,CAEV,CAEA,KAAM,CAAAoC,OAAO,CAAG,CAAAP,aAAa,SAAbA,aAAa,kBAAAP,mBAAA,CAAbO,aAAa,CAAED,IAAI,UAAAN,mBAAA,iBAAnBA,mBAAA,CAAqBc,OAAO,GAAI,EAAE,CAClD,KAAM,CAAAC,cAAc,CAAG,CAAAR,aAAa,SAAbA,aAAa,kBAAAN,oBAAA,CAAbM,aAAa,CAAED,IAAI,UAAAL,oBAAA,iBAAnBA,oBAAA,CAAqBe,OAAO,GAAI,CAAC,CAAC,CACzD,KAAM,CAAAC,SAAS,CAAGV,aAAa,SAAbA,aAAa,kBAAAL,oBAAA,CAAbK,aAAa,CAAED,IAAI,UAAAJ,oBAAA,iBAAnBA,oBAAA,CAAqBe,SAAS,CAEhD;AACA,KAAM,CAAAC,mBAAmB,CAAI/C,KAAK,EAAK,CACrC,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,OAAO,CAC/B,GAAIA,KAAK,EAAI,EAAE,CAAE,MAAO,SAAS,CACjC,MAAO,SAAS,CAClB,CAAC,CAED;AACA,KAAM,CAAAgD,WAAW,CAAIC,KAAK,EAAK,CAC7B,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,KAAK,CAC7B,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAC3C,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,CAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAM,UAAU,CAAC,CAACP,KAAK,CAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,CAAEE,CAAC,CAAC,EAAEzB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGwB,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED,mBACExD,KAAA,CAAC5B,GAAG,EAACqC,EAAE,CAAE,CAAEqD,QAAQ,CAAE,CAAE,CAAE,CAAAnD,QAAA,eACvBb,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,IAAI,CAACR,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAC,kBAExC,CAAY,CAAC,cAGbX,KAAA,CAAC1B,IAAI,EAACyF,SAAS,MAACC,OAAO,CAAE,CAAE,CAACvD,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACxCb,IAAA,CAACxB,IAAI,EAAC2F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAzD,QAAA,cAC9Bb,IAAA,CAACG,UAAU,EACTE,KAAK,CAAC,aAAa,CACnBC,KAAK,IAAAuB,MAAA,CAAK,EAAAS,qBAAA,CAAAY,cAAc,CAACqB,UAAU,UAAAjC,qBAAA,iBAAzBA,qBAAA,CAA2BL,OAAO,CAAC,CAAC,CAAC,GAAI,CAAC,KAAI,CACxD1B,IAAI,cAAEP,IAAA,CAACT,KAAK,GAAE,CAAE,CAChBiB,KAAK,CAAE6C,mBAAmB,CAACH,cAAc,CAACqB,UAAU,EAAI,CAAC,CAAE,CAC3D9D,QAAQ,IAAAoB,MAAA,CAAKqB,cAAc,CAACsB,aAAa,EAAI,CAAC,MAAA3C,MAAA,CAAIqB,cAAc,CAACuB,YAAY,EAAI,CAAC,mBAAkB,CACrG,CAAC,CACE,CAAC,cACPzE,IAAA,CAACxB,IAAI,EAAC2F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAzD,QAAA,cAC9Bb,IAAA,CAACG,UAAU,EACTE,KAAK,CAAC,gBAAgB,CACtBC,KAAK,IAAAuB,MAAA,CAAK,EAAAU,sBAAA,CAAAW,cAAc,CAACwB,aAAa,UAAAnC,sBAAA,iBAA5BA,sBAAA,CAA8BN,OAAO,CAAC,CAAC,CAAC,GAAI,CAAC,KAAI,CAC3D1B,IAAI,cAAEP,IAAA,CAACV,MAAM,GAAE,CAAE,CACjBkB,KAAK,CAAE6C,mBAAmB,CAACH,cAAc,CAACwB,aAAa,EAAI,CAAC,CAAE,CAC9DjE,QAAQ,CAAC,iBAAiB,CAC3B,CAAC,CACE,CAAC,cACPT,IAAA,CAACxB,IAAI,EAAC2F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAzD,QAAA,cAC9Bb,IAAA,CAACG,UAAU,EACTE,KAAK,CAAC,cAAc,CACpBC,KAAK,IAAAuB,MAAA,CAAK,EAAAW,sBAAA,CAAAU,cAAc,CAACyB,WAAW,UAAAnC,sBAAA,iBAA1BA,sBAAA,CAA4BP,OAAO,CAAC,CAAC,CAAC,GAAI,CAAC,KAAI,CACzD1B,IAAI,cAAEP,IAAA,CAACR,OAAO,GAAE,CAAE,CAClBgB,KAAK,CAAE6C,mBAAmB,CAACH,cAAc,CAACyB,WAAW,EAAI,CAAC,CAAE,CAC5DlE,QAAQ,CAAC,qBAAqB,CAC/B,CAAC,CACE,CAAC,cACPT,IAAA,CAACxB,IAAI,EAAC2F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAzD,QAAA,cAC9Bb,IAAA,CAACG,UAAU,EACTE,KAAK,CAAC,eAAe,CACrBC,KAAK,IAAAuB,MAAA,CAAKqB,cAAc,CAACsB,aAAa,EAAI,CAAC,MAAA3C,MAAA,CAAIqB,cAAc,CAACuB,YAAY,EAAI,CAAC,CAAG,CAClFlE,IAAI,cAAEP,IAAA,CAACN,WAAW,GAAE,CAAE,CACtBc,KAAK,CAAE0C,cAAc,CAACsB,aAAa,GAAKtB,cAAc,CAACuB,YAAY,CAAG,SAAS,CAAG,SAAU,CAC5FhE,QAAQ,CAAC,gBAAgB,CAC1B,CAAC,CACE,CAAC,EACH,CAAC,cAGPP,KAAA,CAACvB,KAAK,EAACgC,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAC,CAAEvD,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACzBb,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,IAAI,CAACR,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAC,gBAExC,CAAY,CAAC,cACbb,IAAA,CAACf,cAAc,EAAA4B,QAAA,cACbX,KAAA,CAACpB,KAAK,EAAA+B,QAAA,eACJb,IAAA,CAACd,SAAS,EAAA2B,QAAA,cACRX,KAAA,CAACf,QAAQ,EAAA0B,QAAA,eACPb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,WAAS,CAAW,CAAC,cAChCb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,WAAS,CAAW,CAAC,cAChCb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,QAAM,CAAW,CAAC,cAC7Bb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3Bb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,KAAG,CAAW,CAAC,cAC1Bb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,QAAM,CAAW,CAAC,cAC7Bb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3Bb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CAAC,SAAO,CAAW,CAAC,EACtB,CAAC,CACF,CAAC,cACZb,IAAA,CAACjB,SAAS,EAAA8B,QAAA,CACPoC,OAAO,CAAC4B,GAAG,CAAEC,MAAM,eAClB5E,KAAA,CAACf,QAAQ,EAAA0B,QAAA,eACPb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,cACRb,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACa,UAAU,CAAC,MAAM,CAAAnB,QAAA,CAC1CiE,MAAM,CAACC,MAAM,CACJ,CAAC,CACJ,CAAC,cACZ7E,KAAA,CAAClB,SAAS,EAAA6B,QAAA,eACRX,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAAN,QAAA,EACxBiE,MAAM,CAACE,IAAI,CAAC,GAAC,CAACF,MAAM,CAACG,IAAI,EAChB,CAAC,cACb/E,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,EAAC,QAC5C,CAACiE,MAAM,CAACI,IAAI,EACR,CAAC,EACJ,CAAC,cACZlF,IAAA,CAAChB,SAAS,EAAA6B,QAAA,cACRb,IAAA,CAACnB,IAAI,EACHyC,KAAK,CAAEwD,MAAM,CAACK,MAAO,CACrB5D,IAAI,CAAC,OAAO,CACZf,KAAK,CAAEsE,MAAM,CAACK,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CACxD5E,IAAI,CAAEuE,MAAM,CAACK,MAAM,GAAK,QAAQ,cAAGnF,IAAA,CAACN,WAAW,GAAE,CAAC,cAAGM,IAAA,CAACL,KAAK,GAAE,CAAE,CAChE,CAAC,CACO,CAAC,cACZK,IAAA,CAAChB,SAAS,EAAA6B,QAAA,CACPiE,MAAM,CAACM,YAAY,cAClBpF,IAAA,CAACnB,IAAI,EACHyC,KAAK,CAAC,YAAY,CAClBC,IAAI,CAAC,OAAO,CACZf,KAAK,CAAC,SAAS,CACfW,OAAO,CAAC,UAAU,CACnB,CAAC,cAEFnB,IAAA,CAACzB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,CAAC,UAElD,CAAY,CACb,CACQ,CAAC,cACZb,IAAA,CAAChB,SAAS,EAAA6B,QAAA,cACRb,IAAA,CAAC1B,GAAG,EAACqC,EAAE,CAAE,CAAE0E,QAAQ,CAAE,GAAI,CAAE,CAAAxE,QAAA,cACzBb,IAAA,CAAC8B,WAAW,EACVxB,KAAK,CAAEwE,MAAM,CAACQ,OAAO,CAACC,GAAG,CAACC,WAAY,CACtClE,KAAK,CAAC,KAAK,CACXd,KAAK,CAAE6C,mBAAmB,CAACyB,MAAM,CAACQ,OAAO,CAACC,GAAG,CAACC,WAAW,CAAE,CAC5D,CAAC,CACC,CAAC,CACG,CAAC,cACZxF,IAAA,CAAChB,SAAS,EAAA6B,QAAA,cACRX,KAAA,CAAC5B,GAAG,EAACqC,EAAE,CAAE,CAAE0E,QAAQ,CAAE,GAAI,CAAE,CAAAxE,QAAA,eACzBb,IAAA,CAAC8B,WAAW,EACVxB,KAAK,CAAEwE,MAAM,CAACQ,OAAO,CAACG,MAAM,CAACD,WAAY,CACzClE,KAAK,CAAC,QAAQ,CACdd,KAAK,CAAE6C,mBAAmB,CAACyB,MAAM,CAACQ,OAAO,CAACG,MAAM,CAACD,WAAW,CAAE,CAC/D,CAAC,cACFtF,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,EAChDyC,WAAW,CAACwB,MAAM,CAACQ,OAAO,CAACG,MAAM,CAACC,IAAI,CAAG,IAAI,CAAG,IAAI,CAAC,CAAC,KAAG,CAACpC,WAAW,CAACwB,MAAM,CAACQ,OAAO,CAACG,MAAM,CAACE,KAAK,CAAG,IAAI,CAAG,IAAI,CAAC,EACvG,CAAC,EACV,CAAC,CACG,CAAC,cACZ3F,IAAA,CAAChB,SAAS,EAAA6B,QAAA,cACRX,KAAA,CAAC5B,GAAG,EAACqC,EAAE,CAAE,CAAE0E,QAAQ,CAAE,GAAI,CAAE,CAAAxE,QAAA,eACzBb,IAAA,CAAC8B,WAAW,EACVxB,KAAK,CAAEwE,MAAM,CAACQ,OAAO,CAACM,IAAI,CAACJ,WAAY,CACvClE,KAAK,CAAC,MAAM,CACZd,KAAK,CAAE6C,mBAAmB,CAACyB,MAAM,CAACQ,OAAO,CAACM,IAAI,CAACJ,WAAW,CAAE,CAC7D,CAAC,cACFtF,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,EAChDyC,WAAW,CAACwB,MAAM,CAACQ,OAAO,CAACM,IAAI,CAACF,IAAI,CAAG,IAAI,CAAG,IAAI,CAAC,CAAC,KAAG,CAACpC,WAAW,CAACwB,MAAM,CAACQ,OAAO,CAACM,IAAI,CAACD,KAAK,CAAG,IAAI,CAAG,IAAI,CAAC,EACnG,CAAC,EACV,CAAC,CACG,CAAC,cACZ3F,IAAA,CAAChB,SAAS,EAAA6B,QAAA,cACRX,KAAA,CAAC5B,GAAG,EAAAuC,QAAA,eACFX,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAC0E,QAAQ,CAAC,SAAS,CAAAhF,QAAA,eAC5Cb,IAAA,CAACP,YAAY,EAACkB,EAAE,CAAE,CAAEkF,QAAQ,CAAE,EAAE,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,CAC9CxC,WAAW,CAACwB,MAAM,CAACQ,OAAO,CAACS,OAAO,CAACC,OAAO,CAAC,CAAC,OAC/C,EAAY,CAAC,cACb9F,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAC0E,QAAQ,CAAC,SAAS,CAAAhF,QAAA,EAC3CyC,WAAW,CAACwB,MAAM,CAACQ,OAAO,CAACS,OAAO,CAACE,QAAQ,CAAC,CAAC,QAChD,EAAY,CAAC,cACb/F,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,EAChDiE,MAAM,CAACQ,OAAO,CAACS,OAAO,CAACG,cAAc,CAAC,QACzC,EAAY,CAAC,EACV,CAAC,CACG,CAAC,GAlFCpB,MAAM,CAACC,MAmFZ,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CAAC,EACZ,CAAC,cAGR/E,IAAA,CAAC1B,GAAG,EAACqC,EAAE,CAAE,CAAEwF,SAAS,CAAE,QAAQ,CAAE1E,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,cACtCX,KAAA,CAAC3B,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACX,KAAK,CAAC,eAAe,CAAAK,QAAA,EAAC,gBACpC,CAACuC,SAAS,CAAG,GAAI,CAAAgD,IAAI,CAAChD,SAAS,CAAC,CAACiD,cAAc,CAAC,CAAC,CAAG,SAAS,EACjE,CAAC,CACV,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}