{"ast": null, "code": "import React from'react';import{Navigate}from'react-router-dom';import{Box,Typography,Button}from'@mui/material';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminRoute=_ref=>{let{children}=_ref;const{user,isAdmin}=useAuth();if(!user){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}if(user.role!=='admin'){return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',height:'50vh',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Access Denied\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",textAlign:\"center\",children:\"You don't have permission to access this page. Admin privileges are required.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:()=>window.history.back(),children:\"Go Back\"})]});}return children;};export default AdminRoute;", "map": {"version": 3, "names": ["React", "Navigate", "Box", "Typography", "<PERSON><PERSON>", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "AdminRoute", "_ref", "children", "user", "isAdmin", "to", "replace", "role", "sx", "display", "flexDirection", "alignItems", "justifyContent", "height", "gap", "variant", "component", "gutterBottom", "color", "textAlign", "onClick", "window", "history", "back"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/AdminRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { Box, Typography, Button } from '@mui/material';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst AdminRoute = ({ children }) => {\n  const { user, isAdmin } = useAuth();\n\n  if (!user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (user.role !== 'admin') {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '50vh',\n          gap: 2,\n        }}\n      >\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Access Denied\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" textAlign=\"center\">\n          You don't have permission to access this page. Admin privileges are required.\n        </Typography>\n        <Button variant=\"contained\" onClick={() => window.history.back()}>\n          Go Back\n        </Button>\n      </Box>\n    );\n  }\n\n  return children;\n};\n\nexport default AdminRoute; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,OAASC,GAAG,CAAEC,UAAU,CAAEC,MAAM,KAAQ,eAAe,CACvD,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC9B,KAAM,CAAEE,IAAI,CAAEC,OAAQ,CAAC,CAAGT,OAAO,CAAC,CAAC,CAEnC,GAAI,CAACQ,IAAI,CAAE,CACT,mBAAON,IAAA,CAACN,QAAQ,EAACc,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA,GAAIH,IAAI,CAACI,IAAI,GAAK,OAAO,CAAE,CACzB,mBACER,KAAA,CAACP,GAAG,EACFgB,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,MAAM,CAAE,MAAM,CACdC,GAAG,CAAE,CACP,CAAE,CAAAZ,QAAA,eAEFL,IAAA,CAACJ,UAAU,EAACsB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAf,QAAA,CAAC,eAErD,CAAY,CAAC,cACbL,IAAA,CAACJ,UAAU,EAACsB,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAACC,SAAS,CAAC,QAAQ,CAAAjB,QAAA,CAAC,+EAEtE,CAAY,CAAC,cACbL,IAAA,CAACH,MAAM,EAACqB,OAAO,CAAC,WAAW,CAACK,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE,CAAArB,QAAA,CAAC,SAElE,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}