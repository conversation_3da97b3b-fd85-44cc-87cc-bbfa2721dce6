{"ast": null, "code": "import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000/api';const api=axios.create({baseURL:API_BASE_URL,timeout:30000,headers:{'Content-Type':'application/json'}});// Request interceptor\napi.interceptors.request.use(config=>{// Add auth headers\nconst token=localStorage.getItem('token');if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// Response interceptor\napi.interceptors.response.use(response=>{return response.data;},error=>{if(error.response){var _error$response$data$;// Handle auth errors\nif(error.response.status===401){localStorage.removeItem('token');localStorage.removeItem('user');// Only redirect if we're not already on the login page\nif(!window.location.pathname.includes('/login')){window.location.href='/login';}}// Server responded with error status\nthrow new Error(error.response.data.message||((_error$response$data$=error.response.data.error)===null||_error$response$data$===void 0?void 0:_error$response$data$.message)||'Server error');}else if(error.request){// Request made but no response\nthrow new Error('No response from server');}else{// Something else happened\nthrow new Error(error.message||'Unknown error');}});// Topics API\nexport const topicsApi={getAll:function(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};return api.get('/topics',{params});},getAllWithCounts:()=>api.get('/topics',{params:{includeCounts:'true'}}),getById:topicName=>api.get(\"/topics/\".concat(topicName)),create:topicData=>api.post('/topics',topicData),update:(topicName,configs)=>api.put(\"/topics/\".concat(topicName),{configs}),delete:topicName=>api.delete(\"/topics/\".concat(topicName)),addPartitions:(topicName,partitionCount)=>api.post(\"/topics/\".concat(topicName,\"/partitions\"),{partitionCount}),getMessages:(topicName,params)=>api.get(\"/topics/\".concat(topicName,\"/messages\"),{params}),searchMessages:(topicName,params)=>api.get(\"/topics/\".concat(topicName,\"/search\"),{params}),produceMessage:(topicName,message)=>api.post(\"/topics/\".concat(topicName,\"/messages\"),message),subscribe:topicName=>api.post(\"/topics/\".concat(topicName,\"/subscribe\")),unsubscribe:topicName=>api.post(\"/topics/\".concat(topicName,\"/unsubscribe\")),// New methods for on-demand message count loading\ngetMessageCount:topicName=>api.get(\"/topics/\".concat(topicName,\"/message-count\")),getMessageCounts:topicNames=>api.post('/topics/message-counts',{topicNames})};// Consumer Groups API\nexport const consumerGroupsApi={getAll:()=>api.get('/consumers'),getById:groupId=>api.get(\"/consumers/\".concat(groupId)),delete:groupId=>api.delete(\"/consumers/\".concat(groupId))};// Producers API\nexport const producersApi={bulkProduce:data=>api.post('/producers/bulk',data)};// Cluster API\nexport const clusterApi={getInfo:()=>api.get('/cluster/info'),getHealth:()=>api.get('/cluster/health'),getMessageFlow:()=>api.get('/cluster/message-flow'),getBrokerMetrics:()=>api.get('/cluster/broker-metrics'),getMessageRate:()=>api.get('/cluster/message-rate')};// Config API\nexport const configApi={get:()=>api.get('/config'),getKafkaStatus:()=>api.get('/config/kafka-status')};// Auth API\nexport const authApi={login:credentials=>api.post('/auth/login',credentials),logout:()=>api.post('/auth/logout'),getProfile:()=>api.get('/auth/profile'),updateProfile:profileData=>api.put('/auth/profile',profileData),changePassword:passwordData=>api.put('/auth/change-password',passwordData),register:userData=>api.post('/auth/register',userData),setup:adminData=>api.post('/auth/setup',adminData),getUsers:()=>api.get('/auth/users'),updateUser:(userId,userData)=>api.put(\"/auth/users/\".concat(userId),userData),deleteUser:userId=>api.delete(\"/auth/users/\".concat(userId)),// New role-based endpoints\ngetUserTopics:()=>api.get('/auth/user/topics'),assignTopicToUser:(userId,topicData)=>api.post(\"/auth/users/\".concat(userId,\"/topics\"),topicData),removeTopicFromUser:(userId,topicName)=>api.delete(\"/auth/users/\".concat(userId,\"/topics/\").concat(topicName)),updateTopicPermissions:(userId,topicName,permissions)=>api.put(\"/auth/users/\".concat(userId,\"/topics/\").concat(topicName),{permissions})};export default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "response", "data", "_error$response$data$", "status", "removeItem", "window", "location", "pathname", "includes", "href", "Error", "message", "topicsApi", "getAll", "params", "arguments", "length", "undefined", "get", "getAllWithCounts", "includeCounts", "getById", "topicName", "topicData", "post", "update", "configs", "put", "delete", "addPartitions", "partitionCount", "getMessages", "searchMessages", "produceMessage", "subscribe", "unsubscribe", "getMessageCount", "getMessageCounts", "topicNames", "consumerGroupsApi", "groupId", "producersApi", "bulkProduce", "clusterApi", "getInfo", "getHealth", "getMessageFlow", "getBrokerMetrics", "getMessageRate", "config<PERSON>pi", "getKafkaStatus", "authApi", "login", "credentials", "logout", "getProfile", "updateProfile", "profileData", "changePassword", "passwordData", "register", "userData", "setup", "adminData", "getUsers", "updateUser", "userId", "deleteUser", "getUserTopics", "assignTopicToUser", "removeTopicFromUser", "updateTopicPermissions", "permissions"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    // Add auth headers\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response) {\n      // Handle auth errors\n      if (error.response.status === 401) {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        // Only redirect if we're not already on the login page\n        if (!window.location.pathname.includes('/login')) {\n          window.location.href = '/login';\n        }\n      }\n      // Server responded with error status\n      throw new Error(error.response.data.message || error.response.data.error?.message || 'Server error');\n    } else if (error.request) {\n      // Request made but no response\n      throw new Error('No response from server');\n    } else {\n      // Something else happened\n      throw new Error(error.message || 'Unknown error');\n    }\n  }\n);\n\n// Topics API\nexport const topicsApi = {\n  getAll: (params = {}) => api.get('/topics', { params }),\n  getAllWithCounts: () => api.get('/topics', { params: { includeCounts: 'true' } }),\n  getById: (topicName) => api.get(`/topics/${topicName}`),\n  create: (topicData) => api.post('/topics', topicData),\n  update: (topicName, configs) => api.put(`/topics/${topicName}`, { configs }),\n  delete: (topicName) => api.delete(`/topics/${topicName}`),\n  addPartitions: (topicName, partitionCount) => \n    api.post(`/topics/${topicName}/partitions`, { partitionCount }),\n  getMessages: (topicName, params) => \n    api.get(`/topics/${topicName}/messages`, { params }),\n  searchMessages: (topicName, params) => \n    api.get(`/topics/${topicName}/search`, { params }),\n  produceMessage: (topicName, message) => \n    api.post(`/topics/${topicName}/messages`, message),\n  subscribe: (topicName) => api.post(`/topics/${topicName}/subscribe`),\n  unsubscribe: (topicName) => api.post(`/topics/${topicName}/unsubscribe`),\n  // New methods for on-demand message count loading\n  getMessageCount: (topicName) => api.get(`/topics/${topicName}/message-count`),\n  getMessageCounts: (topicNames) => api.post('/topics/message-counts', { topicNames }),\n};\n\n// Consumer Groups API\nexport const consumerGroupsApi = {\n  getAll: () => api.get('/consumers'),\n  getById: (groupId) => api.get(`/consumers/${groupId}`),\n  delete: (groupId) => api.delete(`/consumers/${groupId}`),\n};\n\n// Producers API\nexport const producersApi = {\n  bulkProduce: (data) => api.post('/producers/bulk', data),\n};\n\n// Cluster API\nexport const clusterApi = {\n  getInfo: () => api.get('/cluster/info'),\n  getHealth: () => api.get('/cluster/health'),\n  getMessageFlow: () => api.get('/cluster/message-flow'),\n  getBrokerMetrics: () => api.get('/cluster/broker-metrics'),\n  getMessageRate: () => api.get('/cluster/message-rate'),\n};\n\n// Config API\nexport const configApi = {\n  get: () => api.get('/config'),\n  getKafkaStatus: () => api.get('/config/kafka-status'),\n};\n\n// Auth API\nexport const authApi = {\n  login: (credentials) => api.post('/auth/login', credentials),\n  logout: () => api.post('/auth/logout'),\n  getProfile: () => api.get('/auth/profile'),\n  updateProfile: (profileData) => api.put('/auth/profile', profileData),\n  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),\n  register: (userData) => api.post('/auth/register', userData),\n  setup: (adminData) => api.post('/auth/setup', adminData),\n  getUsers: () => api.get('/auth/users'),\n  updateUser: (userId, userData) => api.put(`/auth/users/${userId}`, userData),\n  deleteUser: (userId) => api.delete(`/auth/users/${userId}`),\n  // New role-based endpoints\n  getUserTopics: () => api.get('/auth/user/topics'),\n  assignTopicToUser: (userId, topicData) => api.post(`/auth/users/${userId}/topics`, topicData),\n  removeTopicFromUser: (userId, topicName) => api.delete(`/auth/users/${userId}/topics/${topicName}`),\n  updateTopicPermissions: (userId, topicName, permissions) => \n    api.put(`/auth/users/${userId}/topics/${topicName}`, { permissions }),\n};\n\nexport default api; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,CACV;AACA,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAM,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAd,GAAG,CAACK,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC1BU,QAAQ,EAAK,CACZ,MAAO,CAAAA,QAAQ,CAACC,IAAI,CACtB,CAAC,CACAJ,KAAK,EAAK,CACT,GAAIA,KAAK,CAACG,QAAQ,CAAE,KAAAE,qBAAA,CAClB;AACA,GAAIL,KAAK,CAACG,QAAQ,CAACG,MAAM,GAAK,GAAG,CAAE,CACjCV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC,CAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC,CAC/B;AACA,GAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAChDH,MAAM,CAACC,QAAQ,CAACG,IAAI,CAAG,QAAQ,CACjC,CACF,CACA;AACA,KAAM,IAAI,CAAAC,KAAK,CAACb,KAAK,CAACG,QAAQ,CAACC,IAAI,CAACU,OAAO,IAAAT,qBAAA,CAAIL,KAAK,CAACG,QAAQ,CAACC,IAAI,CAACJ,KAAK,UAAAK,qBAAA,iBAAzBA,qBAAA,CAA2BS,OAAO,GAAI,cAAc,CAAC,CACtG,CAAC,IAAM,IAAId,KAAK,CAACR,OAAO,CAAE,CACxB;AACA,KAAM,IAAI,CAAAqB,KAAK,CAAC,yBAAyB,CAAC,CAC5C,CAAC,IAAM,CACL;AACA,KAAM,IAAI,CAAAA,KAAK,CAACb,KAAK,CAACc,OAAO,EAAI,eAAe,CAAC,CACnD,CACF,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,SAAS,CAAG,CACvBC,MAAM,CAAE,QAAAA,CAAA,KAAC,CAAAC,MAAM,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,OAAK,CAAAhC,GAAG,CAACmC,GAAG,CAAC,SAAS,CAAE,CAAEJ,MAAO,CAAC,CAAC,GACvDK,gBAAgB,CAAEA,CAAA,GAAMpC,GAAG,CAACmC,GAAG,CAAC,SAAS,CAAE,CAAEJ,MAAM,CAAE,CAAEM,aAAa,CAAE,MAAO,CAAE,CAAC,CAAC,CACjFC,OAAO,CAAGC,SAAS,EAAKvC,GAAG,CAACmC,GAAG,YAAAtB,MAAA,CAAY0B,SAAS,CAAE,CAAC,CACvDtC,MAAM,CAAGuC,SAAS,EAAKxC,GAAG,CAACyC,IAAI,CAAC,SAAS,CAAED,SAAS,CAAC,CACrDE,MAAM,CAAEA,CAACH,SAAS,CAAEI,OAAO,GAAK3C,GAAG,CAAC4C,GAAG,YAAA/B,MAAA,CAAY0B,SAAS,EAAI,CAAEI,OAAQ,CAAC,CAAC,CAC5EE,MAAM,CAAGN,SAAS,EAAKvC,GAAG,CAAC6C,MAAM,YAAAhC,MAAA,CAAY0B,SAAS,CAAE,CAAC,CACzDO,aAAa,CAAEA,CAACP,SAAS,CAAEQ,cAAc,GACvC/C,GAAG,CAACyC,IAAI,YAAA5B,MAAA,CAAY0B,SAAS,gBAAe,CAAEQ,cAAe,CAAC,CAAC,CACjEC,WAAW,CAAEA,CAACT,SAAS,CAAER,MAAM,GAC7B/B,GAAG,CAACmC,GAAG,YAAAtB,MAAA,CAAY0B,SAAS,cAAa,CAAER,MAAO,CAAC,CAAC,CACtDkB,cAAc,CAAEA,CAACV,SAAS,CAAER,MAAM,GAChC/B,GAAG,CAACmC,GAAG,YAAAtB,MAAA,CAAY0B,SAAS,YAAW,CAAER,MAAO,CAAC,CAAC,CACpDmB,cAAc,CAAEA,CAACX,SAAS,CAAEX,OAAO,GACjC5B,GAAG,CAACyC,IAAI,YAAA5B,MAAA,CAAY0B,SAAS,cAAaX,OAAO,CAAC,CACpDuB,SAAS,CAAGZ,SAAS,EAAKvC,GAAG,CAACyC,IAAI,YAAA5B,MAAA,CAAY0B,SAAS,cAAY,CAAC,CACpEa,WAAW,CAAGb,SAAS,EAAKvC,GAAG,CAACyC,IAAI,YAAA5B,MAAA,CAAY0B,SAAS,gBAAc,CAAC,CACxE;AACAc,eAAe,CAAGd,SAAS,EAAKvC,GAAG,CAACmC,GAAG,YAAAtB,MAAA,CAAY0B,SAAS,kBAAgB,CAAC,CAC7Ee,gBAAgB,CAAGC,UAAU,EAAKvD,GAAG,CAACyC,IAAI,CAAC,wBAAwB,CAAE,CAAEc,UAAW,CAAC,CACrF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,iBAAiB,CAAG,CAC/B1B,MAAM,CAAEA,CAAA,GAAM9B,GAAG,CAACmC,GAAG,CAAC,YAAY,CAAC,CACnCG,OAAO,CAAGmB,OAAO,EAAKzD,GAAG,CAACmC,GAAG,eAAAtB,MAAA,CAAe4C,OAAO,CAAE,CAAC,CACtDZ,MAAM,CAAGY,OAAO,EAAKzD,GAAG,CAAC6C,MAAM,eAAAhC,MAAA,CAAe4C,OAAO,CAAE,CACzD,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,CAC1BC,WAAW,CAAGzC,IAAI,EAAKlB,GAAG,CAACyC,IAAI,CAAC,iBAAiB,CAAEvB,IAAI,CACzD,CAAC,CAED;AACA,MAAO,MAAM,CAAA0C,UAAU,CAAG,CACxBC,OAAO,CAAEA,CAAA,GAAM7D,GAAG,CAACmC,GAAG,CAAC,eAAe,CAAC,CACvC2B,SAAS,CAAEA,CAAA,GAAM9D,GAAG,CAACmC,GAAG,CAAC,iBAAiB,CAAC,CAC3C4B,cAAc,CAAEA,CAAA,GAAM/D,GAAG,CAACmC,GAAG,CAAC,uBAAuB,CAAC,CACtD6B,gBAAgB,CAAEA,CAAA,GAAMhE,GAAG,CAACmC,GAAG,CAAC,yBAAyB,CAAC,CAC1D8B,cAAc,CAAEA,CAAA,GAAMjE,GAAG,CAACmC,GAAG,CAAC,uBAAuB,CACvD,CAAC,CAED;AACA,MAAO,MAAM,CAAA+B,SAAS,CAAG,CACvB/B,GAAG,CAAEA,CAAA,GAAMnC,GAAG,CAACmC,GAAG,CAAC,SAAS,CAAC,CAC7BgC,cAAc,CAAEA,CAAA,GAAMnE,GAAG,CAACmC,GAAG,CAAC,sBAAsB,CACtD,CAAC,CAED;AACA,MAAO,MAAM,CAAAiC,OAAO,CAAG,CACrBC,KAAK,CAAGC,WAAW,EAAKtE,GAAG,CAACyC,IAAI,CAAC,aAAa,CAAE6B,WAAW,CAAC,CAC5DC,MAAM,CAAEA,CAAA,GAAMvE,GAAG,CAACyC,IAAI,CAAC,cAAc,CAAC,CACtC+B,UAAU,CAAEA,CAAA,GAAMxE,GAAG,CAACmC,GAAG,CAAC,eAAe,CAAC,CAC1CsC,aAAa,CAAGC,WAAW,EAAK1E,GAAG,CAAC4C,GAAG,CAAC,eAAe,CAAE8B,WAAW,CAAC,CACrEC,cAAc,CAAGC,YAAY,EAAK5E,GAAG,CAAC4C,GAAG,CAAC,uBAAuB,CAAEgC,YAAY,CAAC,CAChFC,QAAQ,CAAGC,QAAQ,EAAK9E,GAAG,CAACyC,IAAI,CAAC,gBAAgB,CAAEqC,QAAQ,CAAC,CAC5DC,KAAK,CAAGC,SAAS,EAAKhF,GAAG,CAACyC,IAAI,CAAC,aAAa,CAAEuC,SAAS,CAAC,CACxDC,QAAQ,CAAEA,CAAA,GAAMjF,GAAG,CAACmC,GAAG,CAAC,aAAa,CAAC,CACtC+C,UAAU,CAAEA,CAACC,MAAM,CAAEL,QAAQ,GAAK9E,GAAG,CAAC4C,GAAG,gBAAA/B,MAAA,CAAgBsE,MAAM,EAAIL,QAAQ,CAAC,CAC5EM,UAAU,CAAGD,MAAM,EAAKnF,GAAG,CAAC6C,MAAM,gBAAAhC,MAAA,CAAgBsE,MAAM,CAAE,CAAC,CAC3D;AACAE,aAAa,CAAEA,CAAA,GAAMrF,GAAG,CAACmC,GAAG,CAAC,mBAAmB,CAAC,CACjDmD,iBAAiB,CAAEA,CAACH,MAAM,CAAE3C,SAAS,GAAKxC,GAAG,CAACyC,IAAI,gBAAA5B,MAAA,CAAgBsE,MAAM,YAAW3C,SAAS,CAAC,CAC7F+C,mBAAmB,CAAEA,CAACJ,MAAM,CAAE5C,SAAS,GAAKvC,GAAG,CAAC6C,MAAM,gBAAAhC,MAAA,CAAgBsE,MAAM,aAAAtE,MAAA,CAAW0B,SAAS,CAAE,CAAC,CACnGiD,sBAAsB,CAAEA,CAACL,MAAM,CAAE5C,SAAS,CAAEkD,WAAW,GACrDzF,GAAG,CAAC4C,GAAG,gBAAA/B,MAAA,CAAgBsE,MAAM,aAAAtE,MAAA,CAAW0B,SAAS,EAAI,CAAEkD,WAAY,CAAC,CACxE,CAAC,CAED,cAAe,CAAAzF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}