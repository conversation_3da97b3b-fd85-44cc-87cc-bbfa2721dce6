{"ast": null, "code": "import _objectSpread from\"/home/<USER>/Projects/Kafka-dashboard/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Autocomplete,TextField,CircularProgress,Box}from'@mui/material';import{topicsApi}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TopicAutocomplete=_ref=>{let{value,onChange,label=\"Topic Name\",placeholder=\"Search topics...\",disabled=false,error=false,helperText=\"\",required=false,fullWidth=true,size=\"medium\"}=_ref;const[topics,setTopics]=useState([]);const[loading,setLoading]=useState(false);const[inputValue,setInputValue]=useState('');// Fetch topics on component mount\nuseEffect(()=>{const fetchTopics=async()=>{setLoading(true);try{const response=await topicsApi.getAll();if(response.success&&response.data){setTopics(response.data.map(topic=>topic.name));}}catch(error){console.error('Error fetching topics:',error);}finally{setLoading(false);}};fetchTopics();},[]);// Filter topics based on input\nconst filteredTopics=topics.filter(topic=>topic.toLowerCase().includes(inputValue.toLowerCase()));return/*#__PURE__*/_jsx(Autocomplete,{value:value,onChange:(event,newValue)=>{onChange(newValue);},inputValue:inputValue,onInputChange:(event,newInputValue)=>{setInputValue(newInputValue);},options:filteredTopics,loading:loading,disabled:disabled,freeSolo:true,fullWidth:fullWidth,size:size,renderInput:params=>/*#__PURE__*/_jsx(TextField,_objectSpread(_objectSpread({},params),{},{label:label,placeholder:placeholder,required:required,error:error,helperText:helperText,InputProps:_objectSpread(_objectSpread({},params.InputProps),{},{endAdornment:/*#__PURE__*/_jsxs(Box,{children:[loading?/*#__PURE__*/_jsx(CircularProgress,{color:\"inherit\",size:20}):null,params.InputProps.endAdornment]})})}))});};export default TopicAutocomplete;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Autocomplete", "TextField", "CircularProgress", "Box", "topicsApi", "jsx", "_jsx", "jsxs", "_jsxs", "TopicAutocomplete", "_ref", "value", "onChange", "label", "placeholder", "disabled", "error", "helperText", "required", "fullWidth", "size", "topics", "setTopics", "loading", "setLoading", "inputValue", "setInputValue", "fetchTopics", "response", "getAll", "success", "data", "map", "topic", "name", "console", "filteredTopics", "filter", "toLowerCase", "includes", "event", "newValue", "onInputChange", "newInputValue", "options", "freeSolo", "renderInput", "params", "_objectSpread", "InputProps", "endAdornment", "children", "color"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/TopicAutocomplete.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Autocomplete,\n  TextField,\n  CircularProgress,\n  Box,\n} from '@mui/material';\nimport { topicsApi } from '../services/api';\n\nconst TopicAutocomplete = ({ \n  value, \n  onChange, \n  label = \"Topic Name\", \n  placeholder = \"Search topics...\",\n  disabled = false,\n  error = false,\n  helperText = \"\",\n  required = false,\n  fullWidth = true,\n  size = \"medium\"\n}) => {\n  const [topics, setTopics] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [inputValue, setInputValue] = useState('');\n\n  // Fetch topics on component mount\n  useEffect(() => {\n    const fetchTopics = async () => {\n      setLoading(true);\n      try {\n        const response = await topicsApi.getAll();\n        if (response.success && response.data) {\n          setTopics(response.data.map(topic => topic.name));\n        }\n      } catch (error) {\n        console.error('Error fetching topics:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTopics();\n  }, []);\n\n  // Filter topics based on input\n  const filteredTopics = topics.filter(topic =>\n    topic.toLowerCase().includes(inputValue.toLowerCase())\n  );\n\n  return (\n    <Autocomplete\n      value={value}\n      onChange={(event, newValue) => {\n        onChange(newValue);\n      }}\n      inputValue={inputValue}\n      onInputChange={(event, newInputValue) => {\n        setInputValue(newInputValue);\n      }}\n      options={filteredTopics}\n      loading={loading}\n      disabled={disabled}\n      freeSolo\n      fullWidth={fullWidth}\n      size={size}\n      renderInput={(params) => (\n        <TextField\n          {...params}\n          label={label}\n          placeholder={placeholder}\n          required={required}\n          error={error}\n          helperText={helperText}\n          InputProps={{\n            ...params.InputProps,\n            endAdornment: (\n              <Box>\n                {loading ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                {params.InputProps.endAdornment}\n              </Box>\n            ),\n          }}\n        />\n      )}\n    />\n  );\n};\n\nexport default TopicAutocomplete; "], "mappings": "wIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,YAAY,CACZC,SAAS,CACTC,gBAAgB,CAChBC,GAAG,KACE,eAAe,CACtB,OAASC,SAAS,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAWpB,IAXqB,CACzBC,KAAK,CACLC,QAAQ,CACRC,KAAK,CAAG,YAAY,CACpBC,WAAW,CAAG,kBAAkB,CAChCC,QAAQ,CAAG,KAAK,CAChBC,KAAK,CAAG,KAAK,CACbC,UAAU,CAAG,EAAE,CACfC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CAAG,IAAI,CAChBC,IAAI,CAAG,QACT,CAAC,CAAAV,IAAA,CACC,KAAM,CAACW,MAAM,CAAEC,SAAS,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC2B,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CAEhD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4B,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9BH,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAxB,SAAS,CAACyB,MAAM,CAAC,CAAC,CACzC,GAAID,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrCT,SAAS,CAACM,QAAQ,CAACG,IAAI,CAACC,GAAG,CAACC,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CACnD,CACF,CAAE,MAAOlB,KAAK,CAAE,CACdmB,OAAO,CAACnB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACRQ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAS,cAAc,CAAGf,MAAM,CAACgB,MAAM,CAACJ,KAAK,EACxCA,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,UAAU,CAACa,WAAW,CAAC,CAAC,CACvD,CAAC,CAED,mBACEhC,IAAA,CAACN,YAAY,EACXW,KAAK,CAAEA,KAAM,CACbC,QAAQ,CAAEA,CAAC4B,KAAK,CAAEC,QAAQ,GAAK,CAC7B7B,QAAQ,CAAC6B,QAAQ,CAAC,CACpB,CAAE,CACFhB,UAAU,CAAEA,UAAW,CACvBiB,aAAa,CAAEA,CAACF,KAAK,CAAEG,aAAa,GAAK,CACvCjB,aAAa,CAACiB,aAAa,CAAC,CAC9B,CAAE,CACFC,OAAO,CAAER,cAAe,CACxBb,OAAO,CAAEA,OAAQ,CACjBR,QAAQ,CAAEA,QAAS,CACnB8B,QAAQ,MACR1B,SAAS,CAAEA,SAAU,CACrBC,IAAI,CAAEA,IAAK,CACX0B,WAAW,CAAGC,MAAM,eAClBzC,IAAA,CAACL,SAAS,CAAA+C,aAAA,CAAAA,aAAA,IACJD,MAAM,MACVlC,KAAK,CAAEA,KAAM,CACbC,WAAW,CAAEA,WAAY,CACzBI,QAAQ,CAAEA,QAAS,CACnBF,KAAK,CAAEA,KAAM,CACbC,UAAU,CAAEA,UAAW,CACvBgC,UAAU,CAAAD,aAAA,CAAAA,aAAA,IACLD,MAAM,CAACE,UAAU,MACpBC,YAAY,cACV1C,KAAA,CAACL,GAAG,EAAAgD,QAAA,EACD5B,OAAO,cAAGjB,IAAA,CAACJ,gBAAgB,EAACkD,KAAK,CAAC,SAAS,CAAChC,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,IAAI,CAC/D2B,MAAM,CAACE,UAAU,CAACC,YAAY,EAC5B,CACN,EACD,EACH,CACD,CACH,CAAC,CAEN,CAAC,CAED,cAAe,CAAAzC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}