{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Routes,Route,Navigate}from'react-router-dom';import{Box,CircularProgress,useTheme,useMediaQuery}from'@mui/material';import Navbar from'./components/Layout/Navbar';import Sidebar from'./components/Layout/Sidebar';import ProtectedRoute from'./components/ProtectedRoute';import AdminRoute from'./components/AdminRoute';import Login from'./pages/Login';import Dashboard from'./pages/Dashboard';import Topics from'./pages/Topics';import TopicDetail from'./pages/TopicDetail';import ConsumerGroups from'./pages/ConsumerGroups';import ConsumerGroupDetail from'./pages/ConsumerGroupDetail';import MessageBrowser from'./pages/MessageBrowser';import Producer from'./pages/Producer';import ClusterInfo from'./pages/ClusterInfo';import Analytics from'./pages/Analytics';import Settings from'./pages/Settings';import UserManagement from'./pages/UserManagement';import{AuthProvider,useAuth}from'./contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const drawerWidth=240;function AppContent(){const{user,loading}=useAuth();const[mobileOpen,setMobileOpen]=useState(false);const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('md'));const handleDrawerToggle=()=>{setMobileOpen(!mobileOpen);};// Show loading spinner while checking authentication\nif(loading){return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',height:'100vh',flexDirection:'column',gap:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:60}),/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center'},children:\"Loading Kafka Dashboard...\"})]});}// If not authenticated, show only login route\nif(!user){return/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})})]});}// If authenticated, show the main app with navbar and sidebar\nreturn/*#__PURE__*/_jsxs(Box,{sx:{display:'flex'},children:[/*#__PURE__*/_jsx(Navbar,{user:user,onDrawerToggle:handleDrawerToggle,isMobile:isMobile}),/*#__PURE__*/_jsx(Sidebar,{drawerWidth:drawerWidth,mobileOpen:mobileOpen,onDrawerToggle:handleDrawerToggle,isMobile:isMobile}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,p:{xs:2,sm:3},width:{xs:'100%',md:\"calc(100% - \".concat(drawerWidth,\"px)\")},ml:{xs:0,md:\"\".concat(drawerWidth,\"px\")},mt:{xs:7,sm:8},minHeight:'100vh'},children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:user.role==='admin'?/*#__PURE__*/_jsx(Dashboard,{}):/*#__PURE__*/_jsx(Navigate,{to:\"/topics\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/topics\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Topics,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/topics/:topicName\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(TopicDetail,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/consumer-groups\",element:/*#__PURE__*/_jsx(AdminRoute,{children:/*#__PURE__*/_jsx(ConsumerGroups,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/consumer-groups/:groupId\",element:/*#__PURE__*/_jsx(AdminRoute,{children:/*#__PURE__*/_jsx(ConsumerGroupDetail,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(MessageBrowser,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/producer\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Producer,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/cluster\",element:/*#__PURE__*/_jsx(AdminRoute,{children:/*#__PURE__*/_jsx(ClusterInfo,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/analytics\",element:/*#__PURE__*/_jsx(AdminRoute,{children:/*#__PURE__*/_jsx(Analytics,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(AdminRoute,{children:/*#__PURE__*/_jsx(Settings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/user-management\",element:/*#__PURE__*/_jsx(AdminRoute,{children:/*#__PURE__*/_jsx(UserManagement,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:user.role==='admin'?/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true}):/*#__PURE__*/_jsx(Navigate,{to:\"/topics\",replace:true})})]})})]});}function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(AppContent,{})});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Routes", "Route", "Navigate", "Box", "CircularProgress", "useTheme", "useMediaQuery", "<PERSON><PERSON><PERSON>", "Sidebar", "ProtectedRoute", "AdminRoute", "<PERSON><PERSON>", "Dashboard", "Topics", "TopicDetail", "ConsumerGroups", "ConsumerGroupDetail", "MessageBrowser", "Producer", "ClusterInfo", "Analytics", "Settings", "UserManagement", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "drawerWidth", "A<PERSON><PERSON><PERSON>nt", "user", "loading", "mobileOpen", "setMobileOpen", "theme", "isMobile", "breakpoints", "down", "handleDrawerToggle", "sx", "display", "justifyContent", "alignItems", "height", "flexDirection", "gap", "children", "size", "textAlign", "path", "element", "to", "replace", "onDrawerToggle", "component", "flexGrow", "p", "xs", "sm", "width", "md", "concat", "ml", "mt", "minHeight", "role", "App"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CircularProgress, useTheme, useMediaQuery } from '@mui/material';\nimport Navbar from './components/Layout/Navbar';\nimport Sidebar from './components/Layout/Sidebar';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport AdminRoute from './components/AdminRoute';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport Topics from './pages/Topics';\nimport TopicDetail from './pages/TopicDetail';\nimport ConsumerGroups from './pages/ConsumerGroups';\nimport ConsumerGroupDetail from './pages/ConsumerGroupDetail';\nimport MessageBrowser from './pages/MessageBrowser';\nimport Producer from './pages/Producer';\nimport ClusterInfo from './pages/ClusterInfo';\nimport Analytics from './pages/Analytics';\nimport Settings from './pages/Settings';\nimport UserManagement from './pages/UserManagement';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\n\nconst drawerWidth = 240;\n\nfunction AppContent() {\n  const { user, loading } = useAuth();\n  const [mobileOpen, setMobileOpen] = useState(false);\n  \n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '100vh',\n          flexDirection: 'column',\n          gap: 2,\n        }}\n      >\n        <CircularProgress size={60} />\n        <Box sx={{ textAlign: 'center' }}>\n          Loading Kafka Dashboard...\n        </Box>\n      </Box>\n    );\n  }\n\n  // If not authenticated, show only login route\n  if (!user) {\n    return (\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={<Login />} \n        />\n        <Route \n          path=\"*\" \n          element={<Navigate to=\"/login\" replace />} \n        />\n      </Routes>\n    );\n  }\n\n  // If authenticated, show the main app with navbar and sidebar\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <Navbar \n        user={user} \n        onDrawerToggle={handleDrawerToggle}\n        isMobile={isMobile}\n      />\n      <Sidebar \n        drawerWidth={drawerWidth} \n        mobileOpen={mobileOpen}\n        onDrawerToggle={handleDrawerToggle}\n        isMobile={isMobile}\n      />\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: { xs: 2, sm: 3 },\n          width: { \n            xs: '100%',\n            md: `calc(100% - ${drawerWidth}px)` \n          },\n          ml: { \n            xs: 0,\n            md: `${drawerWidth}px` \n          },\n          mt: { xs: 7, sm: 8 },\n          minHeight: '100vh',\n        }}\n      >\n        <Routes>\n          <Route \n            path=\"/\" \n            element={\n              user.role === 'admin' ? (\n                <Dashboard />\n              ) : (\n                <Navigate to=\"/topics\" replace />\n              )\n            } \n          />\n          <Route \n            path=\"/topics\" \n            element={\n              <ProtectedRoute>\n                <Topics />\n              </ProtectedRoute>\n            } \n          />\n          <Route \n            path=\"/topics/:topicName\" \n            element={\n              <ProtectedRoute>\n                <TopicDetail />\n              </ProtectedRoute>\n            } \n          />\n          <Route \n            path=\"/consumer-groups\" \n            element={\n              <AdminRoute>\n                <ConsumerGroups />\n              </AdminRoute>\n            } \n          />\n          <Route \n            path=\"/consumer-groups/:groupId\" \n            element={\n              <AdminRoute>\n                <ConsumerGroupDetail />\n              </AdminRoute>\n            } \n          />\n          <Route \n            path=\"/messages\" \n            element={\n              <ProtectedRoute>\n                <MessageBrowser />\n              </ProtectedRoute>\n            } \n          />\n          <Route \n            path=\"/producer\" \n            element={\n              <ProtectedRoute>\n                <Producer />\n              </ProtectedRoute>\n            } \n          />\n          <Route \n            path=\"/cluster\" \n            element={\n              <AdminRoute>\n                <ClusterInfo />\n              </AdminRoute>\n            } \n          />\n          <Route \n            path=\"/analytics\" \n            element={\n              <AdminRoute>\n                <Analytics />\n              </AdminRoute>\n            } \n          />\n          <Route \n            path=\"/settings\" \n            element={\n              <AdminRoute>\n                <Settings />\n              </AdminRoute>\n            } \n          />\n          <Route \n            path=\"/user-management\" \n            element={\n              <AdminRoute>\n                <UserManagement />\n              </AdminRoute>\n            } \n          />\n          {/* Redirect any unknown routes based on user role */}\n          <Route path=\"*\" element={\n            user.role === 'admin' ? \n              <Navigate to=\"/\" replace /> : \n              <Navigate to=\"/topics\" replace />\n          } />\n        </Routes>\n      </Box>\n    </Box>\n  );\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,GAAG,CAAEC,gBAAgB,CAAEC,QAAQ,CAAEC,aAAa,KAAQ,eAAe,CAC9E,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,OAAO,KAAM,6BAA6B,CACjD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,MAAM,KAAM,gBAAgB,CACnC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,OAASC,YAAY,CAAEC,OAAO,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/D,KAAM,CAAAC,WAAW,CAAG,GAAG,CAEvB,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,KAAM,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGR,OAAO,CAAC,CAAC,CACnC,KAAM,CAACS,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAAqC,KAAK,CAAG9B,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA+B,QAAQ,CAAG9B,aAAa,CAAC6B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAE5D,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/BL,aAAa,CAAC,CAACD,UAAU,CAAC,CAC5B,CAAC,CAED;AACA,GAAID,OAAO,CAAE,CACX,mBACEJ,KAAA,CAACzB,GAAG,EACFqC,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,MAAM,CAAE,OAAO,CACfC,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,CACP,CAAE,CAAAC,QAAA,eAEFrB,IAAA,CAACtB,gBAAgB,EAAC4C,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BtB,IAAA,CAACvB,GAAG,EAACqC,EAAE,CAAE,CAAES,SAAS,CAAE,QAAS,CAAE,CAAAF,QAAA,CAAC,4BAElC,CAAK,CAAC,EACH,CAAC,CAEV,CAEA;AACA,GAAI,CAAChB,IAAI,CAAE,CACT,mBACEH,KAAA,CAAC5B,MAAM,EAAA+C,QAAA,eACLrB,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,QAAQ,CACbC,OAAO,cAAEzB,IAAA,CAACf,KAAK,GAAE,CAAE,CACpB,CAAC,cACFe,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,GAAG,CACRC,OAAO,cAAEzB,IAAA,CAACxB,QAAQ,EAACkD,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAC3C,CAAC,EACI,CAAC,CAEb,CAEA;AACA,mBACEzB,KAAA,CAACzB,GAAG,EAACqC,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAM,QAAA,eAC3BrB,IAAA,CAACnB,MAAM,EACLwB,IAAI,CAAEA,IAAK,CACXuB,cAAc,CAAEf,kBAAmB,CACnCH,QAAQ,CAAEA,QAAS,CACpB,CAAC,cACFV,IAAA,CAAClB,OAAO,EACNqB,WAAW,CAAEA,WAAY,CACzBI,UAAU,CAAEA,UAAW,CACvBqB,cAAc,CAAEf,kBAAmB,CACnCH,QAAQ,CAAEA,QAAS,CACpB,CAAC,cACFV,IAAA,CAACvB,GAAG,EACFoD,SAAS,CAAC,MAAM,CAChBf,EAAE,CAAE,CACFgB,QAAQ,CAAE,CAAC,CACXC,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBC,KAAK,CAAE,CACLF,EAAE,CAAE,MAAM,CACVG,EAAE,gBAAAC,MAAA,CAAiBjC,WAAW,OAChC,CAAC,CACDkC,EAAE,CAAE,CACFL,EAAE,CAAE,CAAC,CACLG,EAAE,IAAAC,MAAA,CAAKjC,WAAW,MACpB,CAAC,CACDmC,EAAE,CAAE,CAAEN,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBM,SAAS,CAAE,OACb,CAAE,CAAAlB,QAAA,cAEFnB,KAAA,CAAC5B,MAAM,EAAA+C,QAAA,eACLrB,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,GAAG,CACRC,OAAO,CACLpB,IAAI,CAACmC,IAAI,GAAK,OAAO,cACnBxC,IAAA,CAACd,SAAS,GAAE,CAAC,cAEbc,IAAA,CAACxB,QAAQ,EAACkD,EAAE,CAAC,SAAS,CAACC,OAAO,MAAE,CAEnC,CACF,CAAC,cACF3B,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,SAAS,CACdC,OAAO,cACLzB,IAAA,CAACjB,cAAc,EAAAsC,QAAA,cACbrB,IAAA,CAACb,MAAM,GAAE,CAAC,CACI,CACjB,CACF,CAAC,cACFa,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,oBAAoB,CACzBC,OAAO,cACLzB,IAAA,CAACjB,cAAc,EAAAsC,QAAA,cACbrB,IAAA,CAACZ,WAAW,GAAE,CAAC,CACD,CACjB,CACF,CAAC,cACFY,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLzB,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACX,cAAc,GAAE,CAAC,CACR,CACb,CACF,CAAC,cACFW,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,2BAA2B,CAChCC,OAAO,cACLzB,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACV,mBAAmB,GAAE,CAAC,CACb,CACb,CACF,CAAC,cACFU,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,WAAW,CAChBC,OAAO,cACLzB,IAAA,CAACjB,cAAc,EAAAsC,QAAA,cACbrB,IAAA,CAACT,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cACFS,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,WAAW,CAChBC,OAAO,cACLzB,IAAA,CAACjB,cAAc,EAAAsC,QAAA,cACbrB,IAAA,CAACR,QAAQ,GAAE,CAAC,CACE,CACjB,CACF,CAAC,cACFQ,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,UAAU,CACfC,OAAO,cACLzB,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACP,WAAW,GAAE,CAAC,CACL,CACb,CACF,CAAC,cACFO,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLzB,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACN,SAAS,GAAE,CAAC,CACH,CACb,CACF,CAAC,cACFM,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,WAAW,CAChBC,OAAO,cACLzB,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACL,QAAQ,GAAE,CAAC,CACF,CACb,CACF,CAAC,cACFK,IAAA,CAACzB,KAAK,EACJiD,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLzB,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACJ,cAAc,GAAE,CAAC,CACR,CACb,CACF,CAAC,cAEFI,IAAA,CAACzB,KAAK,EAACiD,IAAI,CAAC,GAAG,CAACC,OAAO,CACrBpB,IAAI,CAACmC,IAAI,GAAK,OAAO,cACnBxC,IAAA,CAACxB,QAAQ,EAACkD,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,cAC3B3B,IAAA,CAACxB,QAAQ,EAACkD,EAAE,CAAC,SAAS,CAACC,OAAO,MAAE,CACnC,CAAE,CAAC,EACE,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAEA,QAAS,CAAAc,GAAGA,CAAA,CAAG,CACb,mBACEzC,IAAA,CAACH,YAAY,EAAAwB,QAAA,cACXrB,IAAA,CAACI,UAAU,GAAE,CAAC,CACF,CAAC,CAEnB,CAEA,cAAe,CAAAqC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}