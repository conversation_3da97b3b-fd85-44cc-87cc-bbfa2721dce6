{"ast": null, "code": "import React,{createContext,useContext,useState,useEffect}from'react';import{authApi}from'../services/api';import{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext();export const useAuth=()=>{const context=useContext(AuthContext);if(!context){throw new Error('useAuth must be used within an AuthProvider');}return context;};export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[loading,setLoading]=useState(true);const[userTopics,setUserTopics]=useState(null);// Check if user is logged in on app start\nuseEffect(()=>{const token=localStorage.getItem('token');if(token){fetchUserProfile();}else{setLoading(false);}},[]);const fetchUserProfile=async()=>{try{const response=await authApi.getProfile();if(response.success){setUser(response.data);// Fetch user's accessible topics\nfetchUserTopics();}else{localStorage.removeItem('token');setUser(null);}}catch(error){console.error('Error fetching user profile:',error);localStorage.removeItem('token');setUser(null);}finally{setLoading(false);}};const fetchUserTopics=async()=>{try{const response=await authApi.getUserTopics();if(response.success){setUserTopics(response.data);}}catch(error){console.error('Error fetching user topics:',error);}};const login=async credentials=>{try{const response=await authApi.login(credentials);if(response.success){const{token,user}=response.data;localStorage.setItem('token',token);setUser(user);await fetchUserTopics();return{success:true};}else{return{success:false,message:response.message};}}catch(error){return{success:false,message:error.message||'Login failed'};}};const logout=async()=>{try{await authApi.logout();}catch(error){console.error('Logout error:',error);}finally{localStorage.removeItem('token');setUser(null);setUserTopics(null);}};const updateProfile=async profileData=>{try{const response=await authApi.updateProfile(profileData);if(response.success){setUser(response.data);return{success:true};}else{return{success:false,message:response.message};}}catch(error){return{success:false,message:error.message||'Profile update failed'};}};const changePassword=async passwordData=>{try{const response=await authApi.changePassword(passwordData);if(response.success){return{success:true};}else{return{success:false,message:response.message};}}catch(error){return{success:false,message:error.message||'Password change failed'};}};// Helper functions for role-based access\nconst isAdmin=()=>(user===null||user===void 0?void 0:user.role)==='admin';const isTopicOwner=()=>(user===null||user===void 0?void 0:user.role)==='topic_owner';const isViewer=()=>(user===null||user===void 0?void 0:user.role)==='viewer';const canAccessTopic=function(topicName){let permission=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'read';if(isAdmin())return true;if(!(userTopics!==null&&userTopics!==void 0&&userTopics.topicPermissions))return false;// For non-admin users, check if they have the specific permission for the topic\nconst topicPerms=userTopics.topicPermissions[topicName];return topicPerms&&topicPerms.includes(permission);};const canCreateTopics=()=>{return isAdmin();};const canProduceToTopic=topicName=>{if(isAdmin())return true;return canAccessTopic(topicName,'write');};const canBrowseTopic=topicName=>{if(isAdmin())return true;return canAccessTopic(topicName,'read');};const canDeleteTopic=topicName=>{if(isAdmin())return true;return canAccessTopic(topicName,'delete');};const canConfigureTopic=topicName=>{if(isAdmin())return true;return canAccessTopic(topicName,'configure');};const canAccessAdminPages=()=>{return isAdmin();};const getAccessibleTopics=()=>{return(userTopics===null||userTopics===void 0?void 0:userTopics.accessibleTopics)||[];};const value={user,userTopics,loading,login,logout,updateProfile,changePassword,fetchUserProfile,// Role-based helpers\nisAdmin,isTopicOwner,isViewer,canAccessTopic,canCreateTopics,canProduceToTopic,canBrowseTopic,canDeleteTopic,canConfigureTopic,canAccessAdminPages,getAccessibleTopics};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authApi", "jsx", "_jsx", "AuthContext", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "loading", "setLoading", "userTopics", "setUserTopics", "token", "localStorage", "getItem", "fetchUserProfile", "response", "getProfile", "success", "data", "fetchUserTopics", "removeItem", "error", "console", "getUserTopics", "login", "credentials", "setItem", "message", "logout", "updateProfile", "profileData", "changePassword", "passwordData", "isAdmin", "role", "isTopic<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "canAccessTopic", "topicName", "permission", "arguments", "length", "undefined", "topicPermissions", "topicPerms", "includes", "canCreateTopics", "canProduceToTopic", "canBrowseTopic", "canDeleteTopic", "canConfigureTopic", "canAccessAdminPages", "getAccessibleTopics", "accessibleTopics", "value", "Provider"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authApi } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [userTopics, setUserTopics] = useState(null);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      fetchUserProfile();\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  const fetchUserProfile = async () => {\n    try {\n      const response = await authApi.getProfile();\n      if (response.success) {\n        setUser(response.data);\n        // Fetch user's accessible topics\n        fetchUserTopics();\n      } else {\n        localStorage.removeItem('token');\n        setUser(null);\n      }\n    } catch (error) {\n      console.error('Error fetching user profile:', error);\n      localStorage.removeItem('token');\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUserTopics = async () => {\n    try {\n      const response = await authApi.getUserTopics();\n      if (response.success) {\n        setUserTopics(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching user topics:', error);\n    }\n  };\n\n  const login = async (credentials) => {\n    try {\n      const response = await authApi.login(credentials);\n      if (response.success) {\n        const { token, user } = response.data;\n        localStorage.setItem('token', token);\n        setUser(user);\n        await fetchUserTopics();\n        return { success: true };\n      } else {\n        return { success: false, message: response.message };\n      }\n    } catch (error) {\n      return { \n        success: false, \n        message: error.message || 'Login failed' \n      };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authApi.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('token');\n      setUser(null);\n      setUserTopics(null);\n    }\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await authApi.updateProfile(profileData);\n      if (response.success) {\n        setUser(response.data);\n        return { success: true };\n      } else {\n        return { success: false, message: response.message };\n      }\n    } catch (error) {\n      return { \n        success: false, \n        message: error.message || 'Profile update failed' \n      };\n    }\n  };\n\n  const changePassword = async (passwordData) => {\n    try {\n      const response = await authApi.changePassword(passwordData);\n      if (response.success) {\n        return { success: true };\n      } else {\n        return { success: false, message: response.message };\n      }\n    } catch (error) {\n      return { \n        success: false, \n        message: error.message || 'Password change failed' \n      };\n    }\n  };\n\n  // Helper functions for role-based access\n  const isAdmin = () => user?.role === 'admin';\n  const isTopicOwner = () => user?.role === 'topic_owner';\n  const isViewer = () => user?.role === 'viewer';\n\n  const canAccessTopic = (topicName, permission = 'read') => {\n    if (isAdmin()) return true;\n\n    if (!userTopics?.topicPermissions) return false;\n\n    // For non-admin users, check if they have the specific permission for the topic\n    const topicPerms = userTopics.topicPermissions[topicName];\n    return topicPerms && topicPerms.includes(permission);\n  };\n\n  const canCreateTopics = () => {\n    return isAdmin();\n  };\n\n  const canProduceToTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'write');\n  };\n\n  const canBrowseTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'read');\n  };\n\n  const canDeleteTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'delete');\n  };\n\n  const canConfigureTopic = (topicName) => {\n    if (isAdmin()) return true;\n    return canAccessTopic(topicName, 'configure');\n  };\n\n  const canAccessAdminPages = () => {\n    return isAdmin();\n  };\n\n  const getAccessibleTopics = () => {\n    return userTopics?.accessibleTopics || [];\n  };\n\n  const value = {\n    user,\n    userTopics,\n    loading,\n    login,\n    logout,\n    updateProfile,\n    changePassword,\n    fetchUserProfile,\n    // Role-based helpers\n    isAdmin,\n    isTopicOwner,\n    isViewer,\n    canAccessTopic,\n    canCreateTopics,\n    canProduceToTopic,\n    canBrowseTopic,\n    canDeleteTopic,\n    canConfigureTopic,\n    canAccessAdminPages,\n    getAccessibleTopics,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC7E,OAASC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE1C,KAAM,CAAAC,WAAW,cAAGP,aAAa,CAAC,CAAC,CAEnC,MAAO,MAAM,CAAAQ,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGR,UAAU,CAACM,WAAW,CAAC,CACvC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAElD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiB,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTG,gBAAgB,CAAC,CAAC,CACpB,CAAC,IAAM,CACLN,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAM,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAApB,OAAO,CAACqB,UAAU,CAAC,CAAC,CAC3C,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpBX,OAAO,CAACS,QAAQ,CAACG,IAAI,CAAC,CACtB;AACAC,eAAe,CAAC,CAAC,CACnB,CAAC,IAAM,CACLP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC,CAChCd,OAAO,CAAC,IAAI,CAAC,CACf,CACF,CAAE,MAAOe,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpDT,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC,CAChCd,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAW,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAApB,OAAO,CAAC4B,aAAa,CAAC,CAAC,CAC9C,GAAIR,QAAQ,CAACE,OAAO,CAAE,CACpBP,aAAa,CAACK,QAAQ,CAACG,IAAI,CAAC,CAC9B,CACF,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAG,KAAK,CAAG,KAAO,CAAAC,WAAW,EAAK,CACnC,GAAI,CACF,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAApB,OAAO,CAAC6B,KAAK,CAACC,WAAW,CAAC,CACjD,GAAIV,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAEN,KAAK,CAAEN,IAAK,CAAC,CAAGU,QAAQ,CAACG,IAAI,CACrCN,YAAY,CAACc,OAAO,CAAC,OAAO,CAAEf,KAAK,CAAC,CACpCL,OAAO,CAACD,IAAI,CAAC,CACb,KAAM,CAAAc,eAAe,CAAC,CAAC,CACvB,MAAO,CAAEF,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,IAAM,CACL,MAAO,CAAEA,OAAO,CAAE,KAAK,CAAEU,OAAO,CAAEZ,QAAQ,CAACY,OAAQ,CAAC,CACtD,CACF,CAAE,MAAON,KAAK,CAAE,CACd,MAAO,CACLJ,OAAO,CAAE,KAAK,CACdU,OAAO,CAAEN,KAAK,CAACM,OAAO,EAAI,cAC5B,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAC,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF,KAAM,CAAAjC,OAAO,CAACiC,MAAM,CAAC,CAAC,CACxB,CAAE,MAAOP,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,OAAS,CACRT,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC,CAChCd,OAAO,CAAC,IAAI,CAAC,CACbI,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAmB,aAAa,CAAG,KAAO,CAAAC,WAAW,EAAK,CAC3C,GAAI,CACF,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAApB,OAAO,CAACkC,aAAa,CAACC,WAAW,CAAC,CACzD,GAAIf,QAAQ,CAACE,OAAO,CAAE,CACpBX,OAAO,CAACS,QAAQ,CAACG,IAAI,CAAC,CACtB,MAAO,CAAED,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,IAAM,CACL,MAAO,CAAEA,OAAO,CAAE,KAAK,CAAEU,OAAO,CAAEZ,QAAQ,CAACY,OAAQ,CAAC,CACtD,CACF,CAAE,MAAON,KAAK,CAAE,CACd,MAAO,CACLJ,OAAO,CAAE,KAAK,CACdU,OAAO,CAAEN,KAAK,CAACM,OAAO,EAAI,uBAC5B,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAI,cAAc,CAAG,KAAO,CAAAC,YAAY,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAApB,OAAO,CAACoC,cAAc,CAACC,YAAY,CAAC,CAC3D,GAAIjB,QAAQ,CAACE,OAAO,CAAE,CACpB,MAAO,CAAEA,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,IAAM,CACL,MAAO,CAAEA,OAAO,CAAE,KAAK,CAAEU,OAAO,CAAEZ,QAAQ,CAACY,OAAQ,CAAC,CACtD,CACF,CAAE,MAAON,KAAK,CAAE,CACd,MAAO,CACLJ,OAAO,CAAE,KAAK,CACdU,OAAO,CAAEN,KAAK,CAACM,OAAO,EAAI,wBAC5B,CAAC,CACH,CACF,CAAC,CAED;AACA,KAAM,CAAAM,OAAO,CAAGA,CAAA,GAAM,CAAA5B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6B,IAAI,IAAK,OAAO,CAC5C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CAAA9B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6B,IAAI,IAAK,aAAa,CACvD,KAAM,CAAAE,QAAQ,CAAGA,CAAA,GAAM,CAAA/B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE6B,IAAI,IAAK,QAAQ,CAE9C,KAAM,CAAAG,cAAc,CAAG,QAAAA,CAACC,SAAS,CAA0B,IAAxB,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,MAAM,CACpD,GAAIP,OAAO,CAAC,CAAC,CAAE,MAAO,KAAI,CAE1B,GAAI,EAACxB,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEkC,gBAAgB,EAAE,MAAO,MAAK,CAE/C;AACA,KAAM,CAAAC,UAAU,CAAGnC,UAAU,CAACkC,gBAAgB,CAACL,SAAS,CAAC,CACzD,MAAO,CAAAM,UAAU,EAAIA,UAAU,CAACC,QAAQ,CAACN,UAAU,CAAC,CACtD,CAAC,CAED,KAAM,CAAAO,eAAe,CAAGA,CAAA,GAAM,CAC5B,MAAO,CAAAb,OAAO,CAAC,CAAC,CAClB,CAAC,CAED,KAAM,CAAAc,iBAAiB,CAAIT,SAAS,EAAK,CACvC,GAAIL,OAAO,CAAC,CAAC,CAAE,MAAO,KAAI,CAC1B,MAAO,CAAAI,cAAc,CAACC,SAAS,CAAE,OAAO,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAU,cAAc,CAAIV,SAAS,EAAK,CACpC,GAAIL,OAAO,CAAC,CAAC,CAAE,MAAO,KAAI,CAC1B,MAAO,CAAAI,cAAc,CAACC,SAAS,CAAE,MAAM,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAW,cAAc,CAAIX,SAAS,EAAK,CACpC,GAAIL,OAAO,CAAC,CAAC,CAAE,MAAO,KAAI,CAC1B,MAAO,CAAAI,cAAc,CAACC,SAAS,CAAE,QAAQ,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAY,iBAAiB,CAAIZ,SAAS,EAAK,CACvC,GAAIL,OAAO,CAAC,CAAC,CAAE,MAAO,KAAI,CAC1B,MAAO,CAAAI,cAAc,CAACC,SAAS,CAAE,WAAW,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAa,mBAAmB,CAAGA,CAAA,GAAM,CAChC,MAAO,CAAAlB,OAAO,CAAC,CAAC,CAClB,CAAC,CAED,KAAM,CAAAmB,mBAAmB,CAAGA,CAAA,GAAM,CAChC,MAAO,CAAA3C,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE4C,gBAAgB,GAAI,EAAE,CAC3C,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,CACZjD,IAAI,CACJI,UAAU,CACVF,OAAO,CACPiB,KAAK,CACLI,MAAM,CACNC,aAAa,CACbE,cAAc,CACdjB,gBAAgB,CAChB;AACAmB,OAAO,CACPE,YAAY,CACZC,QAAQ,CACRC,cAAc,CACdS,eAAe,CACfC,iBAAiB,CACjBC,cAAc,CACdC,cAAc,CACdC,iBAAiB,CACjBC,mBAAmB,CACnBC,mBACF,CAAC,CAED,mBACEvD,IAAA,CAACC,WAAW,CAACyD,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAlD,QAAA,CAChCA,QAAQ,CACW,CAAC,CAE3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}