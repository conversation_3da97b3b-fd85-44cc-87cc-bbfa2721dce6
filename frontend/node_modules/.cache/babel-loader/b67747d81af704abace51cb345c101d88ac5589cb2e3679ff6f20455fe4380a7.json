{"ast": null, "code": "import React from'react';import ReactD<PERSON> from'react-dom/client';import{<PERSON><PERSON><PERSON><PERSON>outer}from'react-router-dom';import{QueryClient,QueryClientProvider}from'react-query';import{ThemeProvider,createTheme}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';import{Toaster}from'react-hot-toast';import App from'./App';// Create a responsive theme\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const theme=createTheme({palette:{primary:{main:'#1976d2'},secondary:{main:'#dc004e'}},typography:{h4:{fontSize:'2.125rem',fontWeight:600,'@media (max-width:600px)':{fontSize:'1.5rem'}},h5:{fontSize:'1.5rem',fontWeight:600,'@media (max-width:600px)':{fontSize:'1.25rem'}},h6:{fontSize:'1.25rem',fontWeight:600,'@media (max-width:600px)':{fontSize:'1.125rem'}},body1:{fontSize:'1rem','@media (max-width:600px)':{fontSize:'0.875rem'}},body2:{fontSize:'0.875rem','@media (max-width:600px)':{fontSize:'0.75rem'}}},components:{MuiButton:{styleOverrides:{root:{textTransform:'none',fontWeight:500}}},MuiCard:{styleOverrides:{root:{borderRadius:8,boxShadow:'0 2px 8px rgba(0,0,0,0.1)'}}},MuiChip:{styleOverrides:{root:{fontWeight:500}}},MuiTableCell:{styleOverrides:{root:{padding:'12px 16px','@media (max-width:600px)':{padding:'8px 12px'}}}}},breakpoints:{values:{xs:0,sm:600,md:960,lg:1280,xl:1920}}});// Create a client\nconst queryClient=new QueryClient({defaultOptions:{queries:{retry:1,refetchOnWindowFocus:false,staleTime:5*60*1000// 5 minutes\n}}});const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(QueryClientProvider,{client:queryClient,children:/*#__PURE__*/_jsxs(BrowserRouter,{children:[/*#__PURE__*/_jsx(App,{}),/*#__PURE__*/_jsx(Toaster,{position:\"top-right\",toastOptions:{duration:4000,style:{background:'#363636',color:'#fff'},success:{duration:3000,iconTheme:{primary:'#4caf50',secondary:'#fff'}},error:{duration:5000,iconTheme:{primary:'#f44336',secondary:'#fff'}}}})]})})]})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QueryClient", "QueryClientProvider", "ThemeProvider", "createTheme", "CssBaseline", "Toaster", "App", "jsx", "_jsx", "jsxs", "_jsxs", "theme", "palette", "primary", "main", "secondary", "typography", "h4", "fontSize", "fontWeight", "h5", "h6", "body1", "body2", "components", "MuiB<PERSON>on", "styleOverrides", "root", "textTransform", "MuiCard", "borderRadius", "boxShadow", "MuiChip", "MuiTableCell", "padding", "breakpoints", "values", "xs", "sm", "md", "lg", "xl", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "client", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "error"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { <PERSON><PERSON>erRouter } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\n\n// Create a responsive theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n  },\n  typography: {\n    h4: {\n      fontSize: '2.125rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.5rem',\n      },\n    },\n    h5: {\n      fontSize: '1.5rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.25rem',\n      },\n    },\n    h6: {\n      fontSize: '1.25rem',\n      fontWeight: 600,\n      '@media (max-width:600px)': {\n        fontSize: '1.125rem',\n      },\n    },\n    body1: {\n      fontSize: '1rem',\n      '@media (max-width:600px)': {\n        fontSize: '0.875rem',\n      },\n    },\n    body2: {\n      fontSize: '0.875rem',\n      '@media (max-width:600px)': {\n        fontSize: '0.75rem',\n      },\n    },\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 8,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiTableCell: {\n      styleOverrides: {\n        root: {\n          padding: '12px 16px',\n          '@media (max-width:600px)': {\n            padding: '8px 12px',\n          },\n        },\n      },\n    },\n  },\n  breakpoints: {\n    values: {\n      xs: 0,\n      sm: 600,\n      md: 960,\n      lg: 1280,\n      xl: 1920,\n    },\n  },\n});\n\n// Create a client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <QueryClientProvider client={queryClient}>\n        <BrowserRouter>\n          <App />\n          <Toaster\n            position=\"top-right\"\n            toastOptions={{\n              duration: 4000,\n              style: {\n                background: '#363636',\n                color: '#fff',\n              },\n              success: {\n                duration: 3000,\n                iconTheme: {\n                  primary: '#4caf50',\n                  secondary: '#fff',\n                },\n              },\n              error: {\n                duration: 5000,\n                iconTheme: {\n                  primary: '#f44336',\n                  secondary: '#fff',\n                },\n              },\n            }}\n          />\n        </BrowserRouter>\n      </QueryClientProvider>\n    </ThemeProvider>\n  </React.StrictMode>\n); "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,OAASC,aAAa,KAAQ,kBAAkB,CAChD,OAASC,WAAW,CAAEC,mBAAmB,KAAQ,aAAa,CAC9D,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,OAASC,OAAO,KAAQ,iBAAiB,CACzC,MAAO,CAAAC,GAAG,KAAM,OAAO,CAEvB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAGR,WAAW,CAAC,CACxBS,OAAO,CAAE,CACPC,OAAO,CAAE,CACPC,IAAI,CAAE,SACR,CAAC,CACDC,SAAS,CAAE,CACTD,IAAI,CAAE,SACR,CACF,CAAC,CACDE,UAAU,CAAE,CACVC,EAAE,CAAE,CACFC,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,GAAG,CACf,0BAA0B,CAAE,CAC1BD,QAAQ,CAAE,QACZ,CACF,CAAC,CACDE,EAAE,CAAE,CACFF,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GAAG,CACf,0BAA0B,CAAE,CAC1BD,QAAQ,CAAE,SACZ,CACF,CAAC,CACDG,EAAE,CAAE,CACFH,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,GAAG,CACf,0BAA0B,CAAE,CAC1BD,QAAQ,CAAE,UACZ,CACF,CAAC,CACDI,KAAK,CAAE,CACLJ,QAAQ,CAAE,MAAM,CAChB,0BAA0B,CAAE,CAC1BA,QAAQ,CAAE,UACZ,CACF,CAAC,CACDK,KAAK,CAAE,CACLL,QAAQ,CAAE,UAAU,CACpB,0BAA0B,CAAE,CAC1BA,QAAQ,CAAE,SACZ,CACF,CACF,CAAC,CACDM,UAAU,CAAE,CACVC,SAAS,CAAE,CACTC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,aAAa,CAAE,MAAM,CACrBT,UAAU,CAAE,GACd,CACF,CACF,CAAC,CACDU,OAAO,CAAE,CACPH,cAAc,CAAE,CACdC,IAAI,CAAE,CACJG,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,2BACb,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPN,cAAc,CAAE,CACdC,IAAI,CAAE,CACJR,UAAU,CAAE,GACd,CACF,CACF,CAAC,CACDc,YAAY,CAAE,CACZP,cAAc,CAAE,CACdC,IAAI,CAAE,CACJO,OAAO,CAAE,WAAW,CACpB,0BAA0B,CAAE,CAC1BA,OAAO,CAAE,UACX,CACF,CACF,CACF,CACF,CAAC,CACDC,WAAW,CAAE,CACXC,MAAM,CAAE,CACNC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,IAAI,CACRC,EAAE,CAAE,IACN,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAA1C,WAAW,CAAC,CAClC2C,cAAc,CAAE,CACdC,OAAO,CAAE,CACPC,KAAK,CAAE,CAAC,CACRC,oBAAoB,CAAE,KAAK,CAC3BC,SAAS,CAAE,CAAC,CAAG,EAAE,CAAG,IAAM;AAC5B,CACF,CACF,CAAC,CAAC,CAEF,KAAM,CAAApB,IAAI,CAAG7B,QAAQ,CAACkD,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEvB,IAAI,CAACwB,MAAM,cACT3C,IAAA,CAACX,KAAK,CAACuD,UAAU,EAAAC,QAAA,cACf3C,KAAA,CAACR,aAAa,EAACS,KAAK,CAAEA,KAAM,CAAA0C,QAAA,eAC1B7C,IAAA,CAACJ,WAAW,GAAE,CAAC,cACfI,IAAA,CAACP,mBAAmB,EAACqD,MAAM,CAAEZ,WAAY,CAAAW,QAAA,cACvC3C,KAAA,CAACX,aAAa,EAAAsD,QAAA,eACZ7C,IAAA,CAACF,GAAG,GAAE,CAAC,cACPE,IAAA,CAACH,OAAO,EACNkD,QAAQ,CAAC,WAAW,CACpBC,YAAY,CAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,KAAK,CAAE,CACLC,UAAU,CAAE,SAAS,CACrBC,KAAK,CAAE,MACT,CAAC,CACDC,OAAO,CAAE,CACPJ,QAAQ,CAAE,IAAI,CACdK,SAAS,CAAE,CACTjD,OAAO,CAAE,SAAS,CAClBE,SAAS,CAAE,MACb,CACF,CAAC,CACDgD,KAAK,CAAE,CACLN,QAAQ,CAAE,IAAI,CACdK,SAAS,CAAE,CACTjD,OAAO,CAAE,SAAS,CAClBE,SAAS,CAAE,MACb,CACF,CACF,CAAE,CACH,CAAC,EACW,CAAC,CACG,CAAC,EACT,CAAC,CACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}