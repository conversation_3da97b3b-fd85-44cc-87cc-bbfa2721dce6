{"ast": null, "code": "import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children}=_ref;const location=useLocation();const{user}=useAuth();if(!user){// Redirect to login page with return url\nreturn/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}return children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsx", "_jsx", "ProtectedRoute", "_ref", "children", "location", "user", "to", "state", "from", "replace"], "sources": ["/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst ProtectedRoute = ({ children }) => {\n  const location = useLocation();\n  const { user } = useAuth();\n\n  if (!user) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElD,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAClC,KAAM,CAAAE,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEQ,IAAK,CAAC,CAAGP,OAAO,CAAC,CAAC,CAE1B,GAAI,CAACO,IAAI,CAAE,CACT;AACA,mBAAOL,IAAA,CAACJ,QAAQ,EAACU,EAAE,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,IAAI,CAAEJ,QAAS,CAAE,CAACK,OAAO,MAAE,CAAC,CACpE,CAEA,MAAO,CAAAN,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}