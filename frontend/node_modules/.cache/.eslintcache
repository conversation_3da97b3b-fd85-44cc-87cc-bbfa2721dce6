[{"/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js": "1", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js": "2", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroups.js": "3", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Dashboard.js": "4", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroupDetail.js": "5", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Topics.js": "6", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ClusterInfo.js": "7", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/MessageBrowser.js": "8", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Producer.js": "9", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Settings.js": "10", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/TopicDetail.js": "11", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Navbar.js": "12", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js": "13", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js": "14", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/socket.js": "15", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Login.js": "16", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js": "17", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js": "18", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/contexts/AuthContext.js": "19", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/UserManagement.js": "20", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/AdminRoute.js": "21", "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/TopicAutocomplete.js": "22"}, {"size": 3177, "mtime": 1751888199075, "results": "23", "hashOfConfig": "24"}, {"size": 5470, "mtime": 1751896616481, "results": "25", "hashOfConfig": "24"}, {"size": 6709, "mtime": 1751875530445, "results": "26", "hashOfConfig": "24"}, {"size": 8590, "mtime": 1751889099210, "results": "27", "hashOfConfig": "24"}, {"size": 6282, "mtime": 1751631936614, "results": "28", "hashOfConfig": "24"}, {"size": 15929, "mtime": 1751896619889, "results": "29", "hashOfConfig": "24"}, {"size": 8745, "mtime": 1751631930603, "results": "30", "hashOfConfig": "24"}, {"size": 24364, "mtime": 1751889065121, "results": "31", "hashOfConfig": "24"}, {"size": 9650, "mtime": 1751631928592, "results": "32", "hashOfConfig": "24"}, {"size": 7710, "mtime": 1751631934963, "results": "33", "hashOfConfig": "24"}, {"size": 18286, "mtime": 1751884544652, "results": "34", "hashOfConfig": "24"}, {"size": 7519, "mtime": 1751891322068, "results": "35", "hashOfConfig": "24"}, {"size": 4353, "mtime": 1751892987409, "results": "36", "hashOfConfig": "24"}, {"size": 4528, "mtime": 1751891175868, "results": "37", "hashOfConfig": "24"}, {"size": 1614, "mtime": 1751631890462, "results": "38", "hashOfConfig": "24"}, {"size": 7834, "mtime": 1751891302111, "results": "39", "hashOfConfig": "24"}, {"size": 437, "mtime": 1751891291919, "results": "40", "hashOfConfig": "24"}, {"size": 10563, "mtime": 1751882224072, "results": "41", "hashOfConfig": "24"}, {"size": 5006, "mtime": 1751953134225, "results": "42", "hashOfConfig": "24"}, {"size": 18218, "mtime": 1751895219145, "results": "43", "hashOfConfig": "24"}, {"size": 1070, "mtime": 1751896952905, "results": "44", "hashOfConfig": "24"}, {"size": 2160, "mtime": 1751895204800, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y3d95w", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Projects/Kafka-dashboard/frontend/src/index.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/App.js", ["112"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroups.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Dashboard.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ConsumerGroupDetail.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Topics.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/ClusterInfo.js", ["113"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/MessageBrowser.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Producer.js", ["114"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Settings.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/TopicDetail.js", ["115"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Navbar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/Layout/Sidebar.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/api.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/services/socket.js", ["116"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Login.js", ["117"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/Analytics.js", ["118"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/contexts/AuthContext.js", ["119"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/pages/UserManagement.js", ["120", "121", "122", "123", "124"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/AdminRoute.js", ["125"], [], "/home/<USER>/Projects/Kafka-dashboard/frontend/src/components/TopicAutocomplete.js", [], [], {"ruleId": "126", "severity": 1, "message": "127", "line": 1, "column": 27, "nodeType": "128", "messageId": "129", "endLine": 1, "endColumn": 36}, {"ruleId": "126", "severity": 1, "message": "130", "line": 23, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 23, "endColumn": 10}, {"ruleId": "126", "severity": 1, "message": "131", "line": 23, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 23, "endColumn": 10}, {"ruleId": "126", "severity": 1, "message": "132", "line": 40, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 40, "endColumn": 7}, {"ruleId": "133", "severity": 1, "message": "134", "line": 79, "column": 1, "nodeType": "135", "endLine": 79, "endColumn": 36}, {"ruleId": "126", "severity": 1, "message": "136", "line": 26, "column": 10, "nodeType": "128", "messageId": "129", "endLine": 26, "endColumn": 17}, {"ruleId": "126", "severity": 1, "message": "130", "line": 27, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 27, "endColumn": 10}, {"ruleId": "137", "severity": 1, "message": "138", "line": 27, "column": 6, "nodeType": "139", "endLine": 27, "endColumn": 8, "suggestions": "140"}, {"ruleId": "126", "severity": 1, "message": "141", "line": 29, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 29, "endColumn": 12}, {"ruleId": "126", "severity": 1, "message": "142", "line": 30, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 30, "endColumn": 19}, {"ruleId": "126", "severity": 1, "message": "143", "line": 31, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 31, "endColumn": 19}, {"ruleId": "126", "severity": 1, "message": "144", "line": 41, "column": 17, "nodeType": "128", "messageId": "129", "endLine": 41, "endColumn": 31}, {"ruleId": "126", "severity": 1, "message": "145", "line": 42, "column": 13, "nodeType": "128", "messageId": "129", "endLine": 42, "endColumn": 23}, {"ruleId": "126", "severity": 1, "message": "146", "line": 7, "column": 17, "nodeType": "128", "messageId": "129", "endLine": 7, "endColumn": 24}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Warning' is defined but never used.", "'History' is defined but never used.", "'Edit' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'authApi' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["147"], "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'isAdmin' is assigned a value but never used.", {"desc": "148", "fix": "149"}, "Update the dependencies array to be: [fetchUserProfile]", {"range": "150", "text": "151"}, [746, 748], "[fetchUserProfile]"]