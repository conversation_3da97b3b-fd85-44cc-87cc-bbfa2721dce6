import React, { createContext, useContext, useState, useEffect } from 'react';
import { authApi } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userTopics, setUserTopics] = useState(null);

  // Check if user is logged in on app start
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      fetchUserProfile();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUserProfile = async () => {
    try {
      const response = await authApi.getProfile();
      if (response.success) {
        setUser(response.data);
        // Fetch user's accessible topics
        fetchUserTopics();
      } else {
        localStorage.removeItem('token');
        setUser(null);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      localStorage.removeItem('token');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserTopics = async () => {
    try {
      const response = await authApi.getUserTopics();
      if (response.success) {
        setUserTopics(response.data);
      }
    } catch (error) {
      console.error('Error fetching user topics:', error);
    }
  };

  const login = async (credentials) => {
    try {
      const response = await authApi.login(credentials);
      if (response.success) {
        const { token, user } = response.data;
        localStorage.setItem('token', token);
        setUser(user);
        await fetchUserTopics();
        return { success: true };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      return { 
        success: false, 
        message: error.message || 'Login failed' 
      };
    }
  };

  const logout = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('token');
      setUser(null);
      setUserTopics(null);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await authApi.updateProfile(profileData);
      if (response.success) {
        setUser(response.data);
        return { success: true };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      return { 
        success: false, 
        message: error.message || 'Profile update failed' 
      };
    }
  };

  const changePassword = async (passwordData) => {
    try {
      const response = await authApi.changePassword(passwordData);
      if (response.success) {
        return { success: true };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      return { 
        success: false, 
        message: error.message || 'Password change failed' 
      };
    }
  };

  // Helper functions for role-based access
  const isAdmin = () => user?.role === 'admin';
  const isTopicOwner = () => user?.role === 'topic_owner';
  const isViewer = () => user?.role === 'viewer';

  const canAccessTopic = (topicName, permission = 'read') => {
    if (isAdmin()) return true;

    if (!userTopics?.topicPermissions) return false;

    // For non-admin users, check if they have the specific permission for the topic
    const topicPerms = userTopics.topicPermissions[topicName];
    return topicPerms && topicPerms.includes(permission);
  };

  const canCreateTopics = () => {
    return isAdmin();
  };

  const canProduceToTopic = (topicName) => {
    if (isAdmin()) return true;
    return canAccessTopic(topicName, 'write');
  };

  const canBrowseTopic = (topicName) => {
    if (isAdmin()) return true;
    return canAccessTopic(topicName, 'read');
  };

  const canDeleteTopic = (topicName) => {
    if (isAdmin()) return true;
    return canAccessTopic(topicName, 'delete');
  };

  const canConfigureTopic = (topicName) => {
    if (isAdmin()) return true;
    return canAccessTopic(topicName, 'configure');
  };

  const canAccessAdminPages = () => {
    return isAdmin();
  };

  const getAccessibleTopics = () => {
    return userTopics?.accessibleTopics || [];
  };

  const value = {
    user,
    userTopics,
    loading,
    login,
    logout,
    updateProfile,
    changePassword,
    fetchUserProfile,
    // Role-based helpers
    isAdmin,
    isTopicOwner,
    isViewer,
    canAccessTopic,
    canCreateTopics,
    canProduceToTopic,
    canBrowseTopic,
    canDeleteTopic,
    canConfigureTopic,
    canAccessAdminPages,
    getAccessibleTopics,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 