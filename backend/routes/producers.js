const express = require('express');
const joi = require('joi');
const kafkaClient = require('../kafka/kafkaClient');
const logger = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');
const { requireTopicAccess } = require('../middleware/permissions');

const router = express.Router();

// Validation schemas
const bulkProduceSchema = joi.object({
  topic: joi.string().required(),
  messages: joi.array().items(
    joi.object({
      key: joi.string().allow(null, ''),
      value: joi.string().required(),
      headers: joi.object().default({})
    })
  ).required().min(1)
});

// Middleware for validation
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: {
          message: error.details[0].message,
          status: 400
        }
      });
    }
    req.validatedBody = value;
    next();
  };
};

// Middleware to set topicName for permission checking
const setTopicNameFromBody = (req, res, next) => {
  if (req.body && req.body.topic) {
    req.body.topicName = req.body.topic;
  }
  next();
};

// POST /api/producers/bulk - Produce multiple messages to a topic (with write permission)
router.post('/bulk', authenticateToken, validateBody(bulkProduceSchema), setTopicNameFromBody, requireTopicAccess('write'), async (req, res) => {
  try {
    const { topic, messages } = req.validatedBody;
    const results = [];
    
    for (const message of messages) {
      try {
        const result = await kafkaClient.produceMessage(topic, message);
        results.push({ success: true, result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    res.json({
      success: true,
      data: {
        topic,
        totalMessages: messages.length,
        results
      }
    });
  } catch (error) {
    logger.error('Error bulk producing messages:', error);
    res.status(500).json({
      error: {
        message: 'Failed to bulk produce messages',
        status: 500
      }
    });
  }
});

module.exports = router; 