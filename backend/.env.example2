# Kafka Dashboard Backend Environment Variables
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=5000
NODE_ENV=local  # local, qa, or prod

# Kafka Configuration (optional overrides)
# KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=kafka-dashboard
KAFKA_GROUP_ID=kafka-dashboard-group

# For multiple brokers, use comma-separated values:
# KAFKA_BROKERS=broker1:9092,broker2:9092,broker3:9092

# Optional: SSL Configuration
# KAFKA_SSL=true

# Optional: SASL Authentication
# KAFKA_SASL_MECHANISM=PLAIN
# KAFKA_SASL_USERNAME=your-username
# KAFKA_SASL_PASSWORD=your-password

# Environment-specific SASL credentials (REQUIRED for QA/PROD)
# QA Environment
KAFKA_QA_USERNAME=bmskfk
KAFKA_QA_PASSWORD=bkls76298764

# Production Environment
KAFKA_PROD_USERNAME=bmskfk
KAFKA_PROD_PASSWORD=bkls76298764

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/kafka-dashboard

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
