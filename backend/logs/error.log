{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":9804,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-04T12:38:06.660Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:38.448Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:38.449Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:38.463Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:38.463Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:39.526Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:39.526Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:39.528Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:39.528Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:41.537Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:41.538Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:41.556Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:41.556Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:45.554Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:47:45.555Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:45.571Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:47:45.571Z"}
{"code":37,"level":"error","message":"Error adding partitions: Number of partitions is invalid","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Number of partitions is invalid\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/createPartitions/v0/response.js:33:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async [private:Broker:sendRequest] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:904:14)\n    at async Broker.createPartitions (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:577:12)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:232:9\n    at async KafkaClient.addPartitions (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:138:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:189:20","timestamp":"2025-07-05T08:48:11.513Z","type":"INVALID_PARTITIONS"}
{"code":37,"level":"error","message":"Error adding partitions: Number of partitions is invalid","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Number of partitions is invalid\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/createPartitions/v0/response.js:33:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async [private:Broker:sendRequest] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:904:14)\n    at async Broker.createPartitions (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:577:12)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:232:9\n    at async KafkaClient.addPartitions (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:138:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:189:20","timestamp":"2025-07-05T08:48:11.514Z","type":"INVALID_PARTITIONS"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:15.568Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:15.568Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:16.586Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:16.586Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:18.597Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:18.598Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:22.610Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:22.610Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:52.625Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:52.625Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:53.640Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:53.640Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:55.654Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:55.655Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:59.665Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:48:59.665Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:29.676Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:29.676Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:30.684Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:30.684Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:32.695Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:32.695Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:36.709Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:49:36.710Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:06.721Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:06.721Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:07.731Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:07.731Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:09.744Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:09.744Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:13.758Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:13.758Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:43.771Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:43.771Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:44.783Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:44.784Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:46.794Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:46.794Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:50.804Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:50:50.805Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:16.052Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:16.053Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:16.060Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:16.061Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:17.101Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:17.102Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:17.103Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:17.103Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:19.116Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:19.117Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:19.118Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:19.118Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:23.133Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:10:25","timestamp":"2025-07-05T08:51:23.134Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:23.137Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:23.138Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:53.174Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:53.174Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:54.200Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:54.200Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:56.209Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:51:56.209Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:00.217Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:00.217Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:30.231Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:30.231Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:31.246Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:31.246Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:33.261Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:33.262Z"}
{"level":"error","message":"Error fetching cluster info: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:37.280Z"}
{"level":"error","message":"Error checking cluster health: Cannot read properties of undefined (reading 'map')","service":"kafka-dashboard","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at KafkaClient.getClusterInfo (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:298:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/cluster.js:30:25","timestamp":"2025-07-05T08:52:37.280Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":10150,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:41:58.767Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka at startup: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":10150,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:41:58.769Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":5648,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:02.059Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":5648,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:02.059Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching cluster info: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":9882,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:04.864Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching topics: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12068,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:07.759Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching topics: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12068,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:07.759Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":10996,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:11.760Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-06T11:42:11.762Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-06T11:42:11.762Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching topics: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12910,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:19.833Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching topics: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12910,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:19.834Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":5834,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:19.870Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-06T11:42:19.871Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-06T11:42:19.872Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":8638,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:31.370Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":8638,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:31.371Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":13560,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:35.433Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:42:35.434Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:42:35.435Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":8124,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:42:48.561Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:42:48.562Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:42:48.563Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":9492,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:20.009Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:20.010Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:20.011Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching cluster info: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":10728,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:21.670Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":13228,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:23.488Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":13228,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:23.490Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":11804,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:31.114Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:31.115Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:31.115Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":10562,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:36.120Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":10562,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:36.120Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":9336,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:43.079Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:43.080Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:43.080Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":6424,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:44.495Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":6424,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:44.498Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":6924,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:43:54.432Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:54.433Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-06T11:43:54.434Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":14852,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:44:00.647Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Error fetching consumer groups: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":14852,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-06T11:44:00.647Z"}
{"errors":[{"code":38,"name":"KafkaJSCreateTopicError","retriable":false,"topic":"test","type":"INVALID_REPLICATION_FACTOR"}],"level":"error","message":"Error creating topic: Topic creation errors","name":"KafkaJSAggregateError","service":"kafka-dashboard","stack":"KafkaJSAggregateError: Topic creation errors\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/createTopics/v0/response.js:29:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async [private:Broker:sendRequest] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:904:14)\n    at async Broker.createTopics (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:555:12)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:171:9\n    at async KafkaClient.createTopic (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:103:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:90:20","timestamp":"2025-07-06T11:52:49.208Z"}
{"errors":[{"code":38,"name":"KafkaJSCreateTopicError","retriable":false,"topic":"test","type":"INVALID_REPLICATION_FACTOR"}],"level":"error","message":"Error creating topic: Topic creation errors","name":"KafkaJSAggregateError","service":"kafka-dashboard","stack":"KafkaJSAggregateError: Topic creation errors\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/createTopics/v0/response.js:29:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async [private:Broker:sendRequest] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:904:14)\n    at async Broker.createTopics (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:555:12)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:171:9\n    at async KafkaClient.createTopic (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:103:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:90:20","timestamp":"2025-07-06T11:52:49.209Z"}
{"errors":[{"code":38,"name":"KafkaJSCreateTopicError","retriable":false,"topic":"test","type":"INVALID_REPLICATION_FACTOR"}],"level":"error","message":"Error creating topic: Topic creation errors","name":"KafkaJSAggregateError","service":"kafka-dashboard","stack":"KafkaJSAggregateError: Topic creation errors\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/createTopics/v0/response.js:29:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async [private:Broker:sendRequest] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:904:14)\n    at async Broker.createTopics (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:555:12)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:171:9\n    at async KafkaClient.createTopic (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:103:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:90:20","timestamp":"2025-07-06T20:21:09.451Z"}
{"errors":[{"code":38,"name":"KafkaJSCreateTopicError","retriable":false,"topic":"test","type":"INVALID_REPLICATION_FACTOR"}],"level":"error","message":"Error creating topic: Topic creation errors","name":"KafkaJSAggregateError","service":"kafka-dashboard","stack":"KafkaJSAggregateError: Topic creation errors\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/createTopics/v0/response.js:29:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async [private:Broker:sendRequest] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:904:14)\n    at async Broker.createTopics (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:555:12)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:171:9\n    at async KafkaClient.createTopic (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:103:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:90:20","timestamp":"2025-07-06T20:21:09.452Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:09.573Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:09.573Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:14.213Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:14.214Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:14.214Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:33:14.216Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:33:14.216Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:33:14.517Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:15.261Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:15.261Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:15.262Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:33:15.265Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:33:15.266Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:17.278Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:17.279Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:17.280Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:17.304Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:33:17.304Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:33:17.304Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:21.292Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:21.293Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:33:21.294Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:33:21.313Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:33:21.313Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:33:21.313Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:34:27.994Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:34:27.995Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:02.886Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:02.887Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:02.887Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:02.889Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:02.889Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:03.233Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:03.905Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:03.905Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:03.905Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:03.907Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:03.907Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:05.918Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:05.919Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:05.919Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:05.921Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:05.922Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:09.935Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:09.936Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:09.936Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:09.939Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:35:09.940Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:13.914Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:13.915Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:13.915Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:14.931Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:14.932Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:14.932Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:16.947Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:16.948Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:16.948Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:20.959Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:20.960Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:20.961Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:50.975Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:50.978Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:50.978Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:51.996Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:51.997Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:51.997Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:35:54.009Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:54.011Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:35:54.011Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:36:00.961Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:36:33.298Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:36:33.298Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:41:18.993Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:18.994Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:18.994Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:18.996Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:18.996Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:19.340Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:41:20.041Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:20.042Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:20.042Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:20.043Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:20.044Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:41:22.054Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:22.055Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:22.055Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:22.057Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:41:22.057Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:41:26.063Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:26.063Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:41:26.063Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:41:26.072Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:41:26.072Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:41:26.072Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:38.259Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:38.259Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:47.045Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:47.046Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:47.046Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:47.048Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:47.048Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:47.369Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:48.072Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:48.072Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:48.072Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:48.074Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:48.074Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:50.086Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:50.087Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:50.087Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:50.092Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:50:50.093Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:54.099Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:54.100Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:50:54.100Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:50:54.110Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:50:54.110Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:50:54.110Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:52:45.970Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:45.970Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:45.971Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:52:45.973Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:52:45.973Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:52:46.269Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:52:46.994Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:46.995Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:46.995Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:52:46.999Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-07T06:52:47.001Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:52:49.009Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:49.010Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:49.010Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:52:49.025Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:52:49.026Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:52:49.027Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:52:53.030Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:53.031Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:78:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:25:20","timestamp":"2025-07-07T06:52:53.031Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-07T06:52:53.050Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:52:53.051Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:47:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:220:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:10:28","timestamp":"2025-07-07T06:52:53.051Z"}
{"level":"error","message":"Error fetching messages from topic bms-logger-topic: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T10:36:20.358Z"}
{"level":"error","message":"Error fetching messages from topic first_topic: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T10:36:58.171Z"}
{"level":"error","message":"Error fetching messages from topic first_topic: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T10:37:21.760Z"}
{"level":"error","message":"Error fetching messages from topic bms-logger-topic: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T10:38:21.253Z"}
{"level":"error","message":"Error fetching messages from topic cmp_api_request_logs: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T11:15:50.432Z"}
{"level":"error","message":"Error fetching messages from topic cmp_api_request_logs: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T11:18:20.987Z"}
{"level":"error","message":"Error searching messages in topic bms-logger-topic: Message search timeout","service":"kafka-dashboard","stack":"Error: Message search timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:1182:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T11:22:30.768Z"}
{"level":"error","message":"Error fetching messages from topic bms-comm-bulkpromotiontopic: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T11:59:33.661Z"}
{"level":"error","message":"Error fetching messages from topic bms-comm-bulkpromotiontopic: Message fetching timeout","service":"kafka-dashboard","stack":"Error: Message fetching timeout\n    at Timeout._onTimeout (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:533:35)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-07T12:00:02.171Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12942,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T05:57:34.816Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka at startup: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12942,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T05:57:34.818Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12792,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T05:58:26.077Z"}
{"cause":{"broker":"localhost:9092","code":"ECONNREFUSED","name":"KafkaJSConnectionError","retriable":true},"level":"error","message":"Failed to connect to Kafka at startup: Connection error: connect ECONNREFUSED 127.0.0.1:9092","name":"KafkaJSNumberOfRetriesExceeded","retriable":false,"retryCount":5,"retryTime":12792,"service":"kafka-dashboard","stack":"KafkaJSNonRetriableError\n  Caused by: KafkaJSConnectionError: Connection error: connect ECONNREFUSED 127.0.0.1:9092\n    at Socket.onError (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:210:23)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T05:58:26.079Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:00:09.485Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:00:09.488Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:00:35.109Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:00:35.113Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:00:35.113Z"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:00:35.121Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:00:35.122Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:00:35.177Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:00:35.180Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:00:35.181Z"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:00:35.410Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Fallback cluster info fetch also failed: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:00:35.429Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:00:36.189Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:00:36.194Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:00:36.195Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:00:36.293Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:00:36.305Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:00:36.306Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:02:38.743Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:02:38.744Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:00.180Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:00.181Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:11.584Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:03:11.586Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:03:11.587Z"}
{"code":34,"level":"error","message":"Error fetching cluster info: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:03:11.591Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:03:11.873Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Error fetching consumer groups: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:03:11.876Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:11.922Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:03:11.928Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:03:11.930Z"}
{"code":34,"level":"error","message":"Fallback cluster info fetch also failed: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)","timestamp":"2025-07-08T06:03:11.939Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:12.934Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:03:12.936Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:03:12.937Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:13.004Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:03:13.010Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:03:13.011Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:59.449Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:03:59.452Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:05:09.634Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:05:09.636Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:14:39.838Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:14:39.839Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:15:17.728Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:15:17.729Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:15:23.088Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:15:23.090Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:15:23.090Z"}
{"level":"error","message":"Error fetching cluster info: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:15:23.289Z"}
{"level":"error","message":"Error fetching topics: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:15:23.590Z"}
{"level":"error","message":"Error fetching topics: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:15:23.595Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:15:23.890Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:15:23.892Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:15:23.892Z"}
{"level":"error","message":"Fallback cluster info fetch also failed: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:15:24.270Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:15:24.813Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:15:24.815Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:15:24.816Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:15:25.166Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:15:25.168Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:15:25.168Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:16:22.127Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:16:22.129Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:06.780Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:06.781Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:19.058Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:19.060Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:22.617Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:22.619Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:28.200Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:17:28.204Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:17:28.206Z"}
{"level":"error","message":"Error fetching cluster info: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:17:28.401Z"}
{"level":"error","message":"Error fetching topics: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:17:28.726Z"}
{"level":"error","message":"Error fetching topics: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:17:28.733Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:28.973Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:17:28.976Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:17:28.978Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:29.566Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:17:29.567Z"}
{"level":"error","message":"Error fetching consumer groups: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getConsumerGroups (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:348:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/consumers.js:12:28","timestamp":"2025-07-08T06:17:29.568Z"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:17:30.914Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:17:30.918Z"}
{"level":"error","message":"Error fetching topics: Kafka is not available. Please check your Kafka cluster.","service":"kafka-dashboard","stack":"Error: Kafka is not available. Please check your Kafka cluster.\n    at KafkaClient.ensureConnected (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:51:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async KafkaClient.getTopics (/home/<USER>/Projects/Kafka-dashboard/backend/kafka/kafkaClient.js:82:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/routes/topics.js:37:16","timestamp":"2025-07-08T06:17:30.919Z"}
{"level":"error","message":"Fallback cluster info fetch also failed: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async BrokerPool.findConnectedBroker (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:303:5)\n    at async BrokerPool.refreshMetadata (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:153:20)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:190:9","timestamp":"2025-07-08T06:17:34.094Z"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:19:55.294Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:19:55.295Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:20:08.829Z","type":"ILLEGAL_SASL_STATE"}
{"code":34,"level":"error","message":"Failed to connect to Kafka at startup: Request is not valid given the current SASL state","name":"KafkaJSProtocolError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSProtocolError: Request is not valid given the current SASL state\n    at createErrorFromCode (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/error.js:581:10)\n    at Object.parse (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/protocol/requests/saslHandshake/v0/response.js:24:11)\n    at Connection.send (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:433:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:35:23)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14","timestamp":"2025-07-08T06:20:08.832Z","type":"ILLEGAL_SASL_STATE"}
{"level":"error","message":"Failed to connect to Kafka: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:22:21.657Z"}
{"level":"error","message":"Failed to connect to Kafka at startup: SASL PLAIN authentication failed: Authentication failed: Invalid username or password","name":"KafkaJSSASLAuthenticationError","retriable":false,"service":"kafka-dashboard","stack":"KafkaJSSASLAuthenticationError: SASL PLAIN authentication failed: Authentication failed: Invalid username or password\n    at Object.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/plain.js:18:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SASLAuthenticator.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/saslAuthenticator/index.js:73:5)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:139:9\n    at async Connection.authenticate (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:315:5)\n    at async Broker.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/broker/index.js:111:7)\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/brokerPool.js:93:9\n    at async /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:107:14\n    at async Cluster.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/cluster/index.js:146:5)\n    at async Object.connect (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/admin/index.js:87:5)","timestamp":"2025-07-08T06:22:21.659Z"}
{"level":"error","message":"Authentication error: invalid signature","name":"JsonWebTokenError","service":"kafka-dashboard","stack":"JsonWebTokenError: invalid signature\n    at /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/jsonwebtoken/verify.js:171:19\n    at getSecret (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/jsonwebtoken/verify.js:97:14)\n    at module.exports [as verify] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/jsonwebtoken/verify.js:101:10)\n    at authenticateToken (/home/<USER>/Projects/Kafka-dashboard/backend/middleware/auth.js:27:25)\n    at Layer.handle [as handle_request] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-07-08T06:23:28.063Z"}
{"level":"error","message":"Authentication error: invalid signature","name":"JsonWebTokenError","service":"kafka-dashboard","stack":"JsonWebTokenError: invalid signature\n    at /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/jsonwebtoken/verify.js:171:19\n    at getSecret (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/jsonwebtoken/verify.js:97:14)\n    at module.exports [as verify] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/jsonwebtoken/verify.js:101:10)\n    at authenticateToken (/home/<USER>/Projects/Kafka-dashboard/backend/middleware/auth.js:27:25)\n    at Layer.handle [as handle_request] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/express/lib/router/index.js:346:12)","timestamp":"2025-07-08T06:23:28.128Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching cluster info: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:35.760Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching topics: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:35.766Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching cluster info: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:35.768Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching topics: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:35.770Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching consumer groups: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:35.774Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching consumer groups: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:35.776Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Fallback cluster info fetch also failed: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:36.042Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching topics: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:36.042Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Fallback cluster info fetch also failed: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:36.043Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching topics: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:36.045Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching consumer groups: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:37.060Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching consumer groups: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:37.062Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching topics: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:37.332Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching topics: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:23:37.333Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching cluster info: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:24:06.617Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Fallback cluster info fetch also failed: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:24:07.198Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching cluster info: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:24:37.857Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Fallback cluster info fetch also failed: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:24:38.540Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Error fetching cluster info: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:25:08.756Z"}
{"broker":"***********:9092","host":"***********","level":"error","message":"Fallback cluster info fetch also failed: Closed connection","name":"KafkaJSConnectionClosedError","port":9092,"retriable":true,"service":"kafka-dashboard","stack":"KafkaJSConnectionClosedError: Closed connection\n    at Socket.onEnd (/home/<USER>/Projects/Kafka-dashboard/backend/node_modules/kafkajs/src/network/connection.js:197:13)\n    at Socket.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1400:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-07-08T06:25:09.396Z"}
