const User = require('../models/User');
const logger = require('../utils/logger');

// Middleware to check if user is admin
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: {
          message: 'Authentication required',
          status: 401
        }
      });
    }

    if (req.user.role !== 'admin') {
      return res.status(403).json({
        error: {
          message: 'Admin access required',
          status: 403
        }
      });
    }

    next();
  } catch (error) {
    logger.error('Admin permission check failed:', error);
    res.status(500).json({
      error: {
        message: 'Permission check failed',
        status: 500
      }
    });
  }
};

// Middleware to check if user can access a specific topic
const requireTopicAccess = (permission = 'read') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: {
            message: 'Authentication required',
            status: 401
        }
        });
      }

      const topicName = req.params.topicName || req.body.topicName;
      
      if (!topicName) {
        return res.status(400).json({
          error: {
            message: 'Topic name is required',
            status: 400
          }
        });
      }

      // Admin has access to all topics
      if (req.user.role === 'admin') {
        return next();
      }

      // Check if user has access to this topic
      if (!req.user.hasTopicAccess(topicName, permission)) {
        return res.status(403).json({
          error: {
            message: `Access denied to topic: ${topicName}`,
            status: 403
          }
        });
      }

      next();
    } catch (error) {
      logger.error('Topic access permission check failed:', error);
      res.status(500).json({
        error: {
          message: 'Permission check failed',
          status: 500
        }
      });
    }
  };
};

// Middleware to check if user can create topics
const requireTopicCreation = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: {
          message: 'Authentication required',
          status: 401
        }
      });
    }

    if (!req.user.canCreateTopics()) {
      return res.status(403).json({
        error: {
          message: 'Topic creation permission required',
          status: 403
        }
      });
    }

    next();
  } catch (error) {
    logger.error('Topic creation permission check failed:', error);
    res.status(500).json({
      error: {
        message: 'Permission check failed',
        status: 500
      }
    });
  }
};

// Middleware to check if user can perform admin actions
const requireAdminAction = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: {
          message: 'Authentication required',
          status: 401
        }
      });
    }

    if (!req.user.canPerformAdminAction()) {
      return res.status(403).json({
        error: {
          message: 'Admin action permission required',
          status: 403
        }
      });
    }

    next();
  } catch (error) {
    logger.error('Admin action permission check failed:', error);
    res.status(500).json({
      error: {
        message: 'Permission check failed',
        status: 500
      }
    });
  }
};

// Middleware to filter topics based on user permissions
const filterTopicsByPermission = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: {
          message: 'Authentication required',
          status: 401
        }
      });
    }

    // Admin can see all topics
    if (req.user.role === 'admin') {
      return next();
    }

    // For non-admin users, we'll filter topics in the route handler
    // by adding accessible topics to the request object
    req.accessibleTopics = req.user.getAccessibleTopics();
    next();
  } catch (error) {
    logger.error('Topic filtering failed:', error);
    res.status(500).json({
      error: {
        message: 'Permission filtering failed',
        status: 500
      }
    });
  }
};

// Helper function to check if user can access dashboard/cluster/settings
const canAccessAdminPages = (user) => {
  return user && user.role === 'admin';
};

// Helper function to get user's accessible topics
const getAccessibleTopics = (user) => {
  if (!user) return [];
  return user.getAccessibleTopics();
};

// Helper function to check if user can produce to topic
const canProduceToTopic = (user, topicName) => {
  if (!user) return false;
  return user.canProduceToTopic(topicName);
};

// Helper function to check if user can browse topic
const canBrowseTopic = (user, topicName) => {
  if (!user) return false;
  return user.canBrowseTopic(topicName);
};

module.exports = {
  requireAdmin,
  requireTopicAccess,
  requireTopicCreation,
  requireAdminAction,
  filterTopicsByPermission,
  canAccessAdminPages,
  getAccessibleTopics,
  canProduceToTopic,
  canBrowseTopic
}; 