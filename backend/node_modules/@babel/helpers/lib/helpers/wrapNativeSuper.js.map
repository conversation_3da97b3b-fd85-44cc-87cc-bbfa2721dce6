{"version": 3, "names": ["_getPrototypeOf", "require", "_setPrototypeOf", "_isNativeFunction", "_construct", "_wrapNativeSuper", "Class", "_cache", "Map", "undefined", "exports", "default", "isNativeFunction", "TypeError", "has", "get", "set", "Wrapper", "construct", "arguments", "getPrototypeOf", "constructor", "prototype", "Object", "create", "value", "enumerable", "writable", "configurable", "setPrototypeOf"], "sources": ["../../src/helpers/wrapNativeSuper.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\n// Based on https://github.com/WebReflection/babel-plugin-transform-builtin-classes\n\nimport getPrototypeOf from \"./getPrototypeOf.ts\";\nimport setPrototypeOf from \"./setPrototypeOf.ts\";\nimport isNativeFunction from \"./isNativeFunction.ts\";\nimport construct from \"./construct.ts\";\n\nexport default function _wrapNativeSuper(Class: Function | null) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n\n  // @ts-expect-error -- reuse function id for helper size\n  _wrapNativeSuper = function _wrapNativeSuper(Class: Function | null) {\n    if (Class === null || !isNativeFunction(Class)) return Class;\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    if (_cache !== undefined) {\n      if (_cache.has(Class)) return _cache.get(Class);\n      _cache.set(Class, Wrapper);\n    }\n\n    function Wrapper() {\n      // @ts-expect-error -- we are sure Class is a function here\n      return construct(Class, arguments, getPrototypeOf(this).constructor);\n    }\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true,\n      },\n    });\n\n    return setPrototypeOf(Wrapper, Class);\n  };\n\n  return _wrapNativeSuper(Class);\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAEe,SAASI,gBAAgBA,CAACC,KAAsB,EAAE;EAC/D,IAAIC,MAAM,GAAG,OAAOC,GAAG,KAAK,UAAU,GAAG,IAAIA,GAAG,CAAC,CAAC,GAAGC,SAAS;EAG9DC,OAAA,CAAAC,OAAA,GAAAN,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAsB,EAAE;IACnE,IAAIA,KAAK,KAAK,IAAI,IAAI,CAAC,IAAAM,yBAAgB,EAACN,KAAK,CAAC,EAAE,OAAOA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,MAAM,IAAIO,SAAS,CAAC,oDAAoD,CAAC;IAC3E;IACA,IAAIN,MAAM,KAAKE,SAAS,EAAE;MACxB,IAAIF,MAAM,CAACO,GAAG,CAACR,KAAK,CAAC,EAAE,OAAOC,MAAM,CAACQ,GAAG,CAACT,KAAK,CAAC;MAC/CC,MAAM,CAACS,GAAG,CAACV,KAAK,EAAEW,OAAO,CAAC;IAC5B;IAEA,SAASA,OAAOA,CAAA,EAAG;MAEjB,OAAO,IAAAC,kBAAS,EAACZ,KAAK,EAAEa,SAAS,EAAE,IAAAC,uBAAc,EAAC,IAAI,CAAC,CAACC,WAAW,CAAC;IACtE;IACAJ,OAAO,CAACK,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAClB,KAAK,CAACgB,SAAS,EAAE;MACjDD,WAAW,EAAE;QACXI,KAAK,EAAER,OAAO;QACdS,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAEF,OAAO,IAAAC,uBAAc,EAACZ,OAAO,EAAEX,KAAK,CAAC;EACvC,CAAC;EAED,OAAOD,gBAAgB,CAACC,KAAK,CAAC;AAChC", "ignoreList": []}