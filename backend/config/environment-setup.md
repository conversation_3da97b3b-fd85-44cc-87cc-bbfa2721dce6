# Environment Setup Guide

This guide explains how to configure the Kafka Dashboard for different environments.

## Environment Configuration

The application supports three environments:
- **local**: Development environment (default)
- **qa**: Quality Assurance environment
- **prod**: Production environment

## Configuration Priority

The application loads configuration in the following order:
1. Environment variables (highest priority)
2. Environment-specific hardcoded values in `config.js`
3. Default values (lowest priority)

## Environment-Specific Settings

### Local Environment
- **Kafka Brokers**: localhost:9092
- **Authentication**: None (SASL disabled)
- **SSL**: Disabled
- **Client ID**: kafka-dashboard-local

### QA Environment
- **Kafka Brokers**: ***********:9092
- **Authentication**: SASL/PLAIN with credentials
- **Username**: bmskfk
- **Password**: bkls76298764
- **SSL**: Disabled
- **Client ID**: kafka-dashboard-qa

### Production Environment
- **Kafka Brokers**: ***********:9092, ************:9092, ************:9092
- **Authentication**: SASL/PLAIN with credentials
- **Username**: bmskfk
- **Password**: bms76298764
- **SSL**: Disabled
- **Client ID**: kafka-dashboard-prod

## Running the Application

### Using NPM Scripts

```bash
# Local environment
npm run start:local
npm run dev:local

# QA environment
npm run start:qa
npm run dev:qa

# Production environment
npm run start:prod
npm run dev:prod
```

### Using Environment Variables

You can also set the NODE_ENV environment variable directly:

```bash
# Local
NODE_ENV=local npm start

# QA
NODE_ENV=qa npm start

# Production
NODE_ENV=prod npm start
```

## Environment Variables Override

You can override any configuration using environment variables:

```bash
# Override Kafka brokers
KAFKA_BROKERS=custom-broker1:9092,custom-broker2:9092 NODE_ENV=qa npm start

# Override authentication
KAFKA_SASL_USERNAME=custom-user KAFKA_SASL_PASSWORD=custom-pass NODE_ENV=prod npm start

# Override port
PORT=8080 NODE_ENV=qa npm start
```

## Available Environment Variables

- `NODE_ENV`: Environment name (local, qa, prod)
- `PORT`: Server port (default: 5000)
- `CORS_ORIGIN`: CORS origin URL
- `KAFKA_BROKERS`: Comma-separated Kafka broker addresses
- `KAFKA_CLIENT_ID`: Kafka client identifier
- `KAFKA_GROUP_ID`: Kafka consumer group ID
- `KAFKA_CONNECTION_TIMEOUT`: Connection timeout in milliseconds
- `KAFKA_REQUEST_TIMEOUT`: Request timeout in milliseconds
- `KAFKA_SSL`: Enable SSL (true/false)
- `KAFKA_SASL_MECHANISM`: SASL mechanism (plain, scram-sha-256, etc.)
- `KAFKA_SASL_USERNAME`: SASL username
- `KAFKA_SASL_PASSWORD`: SASL password
- `RATE_LIMIT_WINDOW_MS`: Rate limiting window in milliseconds
- `RATE_LIMIT_MAX_REQUESTS`: Maximum requests per window
- `LOG_LEVEL`: Logging level (debug, info, warn, error)

## Docker Support

If using Docker, you can pass environment variables:

```bash
# QA environment
docker run -e NODE_ENV=qa -p 5000:5000 kafka-dashboard-backend

# Production environment
docker run -e NODE_ENV=prod -p 5000:5000 kafka-dashboard-backend
```

## Security Considerations

1. **Never commit credentials** to version control
2. Use environment variables for sensitive data in production
3. Consider using secret management systems for production credentials
4. Rotate credentials regularly
5. Use SSL/TLS in production environments

## Testing Environment Configuration

To test if the configuration is working correctly:

1. Start the server with the desired environment
2. Check the console output for the environment and Kafka broker information
3. The application will log the current environment and connection status
4. Use the `/api/cluster/info` endpoint to verify Kafka connectivity

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if Kafka brokers are accessible
2. **Authentication Failed**: Verify SASL credentials
3. **Timeout Errors**: Increase connection and request timeouts
4. **SSL Issues**: Ensure SSL configuration matches Kafka setup

### Debug Mode

Enable debug logging to see detailed connection information:

```bash
LOG_LEVEL=debug NODE_ENV=qa npm run dev:qa
``` 