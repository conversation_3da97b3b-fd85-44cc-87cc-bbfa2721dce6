require('dotenv').config();

// Environment-specific configurations
const environments = {
  local: {
    kafka: {
      brokers: ['localhost:9092'],
      clientId: 'kafka-dashboard-local',
      groupId: 'kafka-dashboard-group-local',
      connectionTimeout: 3000,
      requestTimeout: 30000,
      ssl: false,
      sasl: null
    },
    jwt: {
      secret: 'local-development-secret-key',
      expiresIn: '24h'
    }
  },
  qa: {
    kafka: {
      brokers: ['***********:9092'],
      clientId: 'kafka-dashboard-qa',
      groupId: 'kafka-dashboard-group-qa',
      connectionTimeout: 5000,
      requestTimeout: 30000,
      ssl: false,
      sasl: {
        mechanism: 'plain',
        username: 'bmskfk',
        password: 'bkls76298764'
      }
    },
    jwt: {
      secret: 'qa-environment-secret-key-change-in-production',
      expiresIn: '24h'
    }
  },
  prod: {
    kafka: {
      brokers: ['***********:9092', '************:9092', '************:9092'],
      clientId: 'kafka-dashboard-prod',
      groupId: 'kafka-dashboard-group-prod',
      connectionTimeout: 10000,
      requestTimeout: 60000,
      ssl: false,
      sasl: {
        mechanism: 'plain',
        username: 'bmskfk',
        password: 'bms76298764'  // Updated password - try this one
      }
    },
    jwt: {
      secret: 'production-secret-key-change-this-in-real-production',
      expiresIn: '24h'
    }
  },
  'prod-no-auth': {
    kafka: {
      brokers: ['***********:9092', '************:9092', '************:9092'],
      clientId: 'kafka-dashboard-prod-no-auth',
      groupId: 'kafka-dashboard-group-prod-no-auth',
      connectionTimeout: 10000,
      requestTimeout: 60000,
      ssl: false,
      sasl: null  // No authentication for testing
    },
    jwt: {
      secret: 'production-secret-key-change-this-in-real-production',
      expiresIn: '24h'
    }
  }
};

// Get current environment
const currentEnv = process.env.NODE_ENV || 'local';

console.log('--------------------------------currentENV: ', currentEnv);

// Get environment-specific config
const envConfig = environments[currentEnv] || environments.local;

console.log('--------------------------------env: ', envConfig);

module.exports = {
  server: {
    port: process.env.PORT || 5000,
    nodeEnv: currentEnv,
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  },
  kafka: {
    brokers: process.env.KAFKA_BROKERS ? process.env.KAFKA_BROKERS.split(',') : envConfig.kafka.brokers,
    clientId: process.env.KAFKA_CLIENT_ID || envConfig.kafka.clientId,
    groupId: process.env.KAFKA_GROUP_ID || envConfig.kafka.groupId,
    connectionTimeout: parseInt(process.env.KAFKA_CONNECTION_TIMEOUT) || envConfig.kafka.connectionTimeout,
    requestTimeout: parseInt(process.env.KAFKA_REQUEST_TIMEOUT) || envConfig.kafka.requestTimeout,
    ssl: process.env.KAFKA_SSL === 'true' || envConfig.kafka.ssl,
    sasl: process.env.KAFKA_SASL_MECHANISM ? {
      mechanism: process.env.KAFKA_SASL_MECHANISM,
      username: process.env.KAFKA_SASL_USERNAME,
      password: process.env.KAFKA_SASL_PASSWORD
    } : envConfig.kafka.sasl
  },
  rateLimiting: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info'
  },
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/kafka-dashboard',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },
  jwt: {
    secret: process.env.JWT_SECRET || envConfig.jwt.secret,
    expiresIn: process.env.JWT_EXPIRES_IN || envConfig.jwt.expiresIn
  },
  environment: currentEnv
};