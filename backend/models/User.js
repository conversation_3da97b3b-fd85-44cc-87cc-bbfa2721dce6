const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['admin', 'topic_owner', 'viewer'],
    default: 'viewer'
  },
  // Topic assignments for topic_owner and viewer roles
  assignedTopics: [{
    topicName: {
      type: String,
      required: true
    },
    permissions: {
      type: [String],
      enum: ['read', 'write', 'delete', 'configure'],
      default: ['read']
    },
    assignedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    assignedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Profile information
  fullName: {
    type: String,
    trim: true,
    maxlength: 100
  },
  department: {
    type: String,
    trim: true,
    maxlength: 100
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: {
    type: Date
  }
}, {
  timestamps: true
});

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Virtual for checking if user is admin
userSchema.virtual('isAdmin').get(function() {
  return this.role === 'admin';
});

// Virtual for checking if user is topic owner
userSchema.virtual('isTopicOwner').get(function() {
  return this.role === 'topic_owner';
});

// Virtual for checking if user is viewer
userSchema.virtual('isViewer').get(function() {
  return this.role === 'viewer';
});

// Method to check if user has access to a specific topic
userSchema.methods.hasTopicAccess = function(topicName, permission = 'read') {
  if (this.role === 'admin') return true;
  
  const topicAssignment = this.assignedTopics.find(t => t.topicName === topicName);
  if (!topicAssignment) return false;
  
  return topicAssignment.permissions.includes(permission);
};

// Method to get all topics user has access to
userSchema.methods.getAccessibleTopics = function() {
  if (this.role === 'admin') return []; // Admin has access to all topics
  return this.assignedTopics.map(t => t.topicName);
};

// Method to get detailed topic permissions
userSchema.methods.getTopicPermissions = function() {
  if (this.role === 'admin') return {}; // Admin has all permissions for all topics
  const permissions = {};
  this.assignedTopics.forEach(topic => {
    permissions[topic.topicName] = topic.permissions;
  });
  return permissions;
};

// Method to check if user can perform admin actions
userSchema.methods.canPerformAdminAction = function() {
  return this.role === 'admin';
};

// Method to check if user can create topics
userSchema.methods.canCreateTopics = function() {
  return this.role === 'admin';
};

// Method to check if user can produce messages to topic
userSchema.methods.canProduceToTopic = function(topicName) {
  if (this.role === 'admin') return true;
  return this.hasTopicAccess(topicName, 'write');
};

// Method to check if user can browse messages from topic
userSchema.methods.canBrowseTopic = function(topicName) {
  if (this.role === 'admin') return true;
  return this.hasTopicAccess(topicName, 'read');
};

// Method to check if user can delete topic
userSchema.methods.canDeleteTopic = function(topicName) {
  if (this.role === 'admin') return true;
  return this.hasTopicAccess(topicName, 'delete');
};

// Method to check if user can configure topic
userSchema.methods.canConfigureTopic = function(topicName) {
  if (this.role === 'admin') return true;
  return this.hasTopicAccess(topicName, 'configure');
};

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (this.isLocked) {
    throw new Error('Account is locked due to too many failed login attempts');
  }
  
  const isMatch = await bcrypt.compare(candidatePassword, this.password);
  
  if (!isMatch) {
    // Increment login attempts
    this.loginAttempts += 1;
    
    // Lock account after 5 failed attempts for 30 minutes
    if (this.loginAttempts >= 5) {
      this.lockUntil = Date.now() + (30 * 60 * 1000); // 30 minutes
    }
    
    await this.save();
    throw new Error('Invalid credentials');
  }
  
  // Reset login attempts on successful login
  if (this.loginAttempts > 0) {
    this.loginAttempts = 0;
    this.lockUntil = undefined;
  }
  
  this.lastLogin = new Date();
  await this.save();
  
  return true;
};

// Remove sensitive data when converting to JSON
userSchema.methods.toJSON = function() {
  const user = this.toObject({ virtuals: true });
  delete user.password;
  delete user.loginAttempts;
  delete user.lockUntil;
  return user;
};

module.exports = mongoose.model('User', userSchema); 